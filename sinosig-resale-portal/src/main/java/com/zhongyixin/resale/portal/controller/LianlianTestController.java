package com.zhongyixin.resale.portal.controller;

import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.ext.lianlianpay.dto.PaymentDTO;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @createDate 2025/8/11 15:18
 */
@Slf4j
@RequestMapping("/callback/lianlian/test")
@RestController
@Tag(name = "连连测试")
public class LianlianTestController {


    @Resource
    LianlianPayApiManager lianlianPayApiManager;

    @GetMapping
    public void testLianlianPayment(){
        //发起代付   0.1*4
        //发送连连代付请求
        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setAmount("0.1");
        paymentDTO.setBankCardNo("6236682950005071621");
        paymentDTO.setCardName("魏加");
        paymentDTO.setOrderId("*********");
        paymentDTO.setPlatform("RONGZHI");
        paymentDTO.setInfoOrder(LianlianPayConstant.QUNQIU_INFO_ORDER);
        try {
            PaymentOut paymentOut = lianlianPayApiManager.payment(paymentDTO);
            System.out.println(paymentOut.successed());
            System.out.println(paymentOut);
        } catch (Exception e) {
            log.error("lianlian payment 接口异常" , e);
        }
    }

}
