package com.zhongyixin.resale.portal.controller;

import com.wftk.common.core.result.ApiResult;
import com.zhongyixin.resale.biz.common.enums.BannerStatusEnum;
import com.zhongyixin.resale.biz.common.output.BannerOutput;
import com.zhongyixin.resale.biz.entity.Banner;
import com.zhongyixin.resale.biz.service.BannerService;
import com.zhongyixin.resale.portal.annotation.namespace.PortalMapping;
import com.zhongyixin.resale.portal.converter.BannerConverter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2025/4/28 17:12
 */
@Tag(name = "业务端-banner相关api")
@PortalMapping("/banner")
public class BannerController {

    @Autowired
    private BannerService bannerService;

    @Autowired
    private BannerConverter bannerConverter;

    @Operation(summary = "根据类型查询banner")
    @GetMapping("/queryBanner")
    public ApiResult<List<BannerOutput>> queryBanner(@RequestParam("type") Integer type, @RequestParam("source") String source){
        List<Banner> banners = bannerService.selectListByType(BannerStatusEnum.NOMAL.getValue(),source , type);
        return ApiResult.ok(bannerConverter.entityToOutput(banners));
    }

    @Operation(summary = "根据source查询所有banner")
    @GetMapping("/queryAllBanner")
    public ApiResult<List<BannerOutput>> queryAllBanner(@RequestParam("source") String source){
        List<Banner> banners  = bannerService.selectListByStatus(BannerStatusEnum.NOMAL.getValue(), source);
        List<BannerOutput> bannerOutputs = bannerConverter.entityToOutput(banners);
        return ApiResult.ok(bannerOutputs);
    }

    @Operation(summary = "根据source查询所有banner")
    @GetMapping("/queryBannerByTypes")
    public ApiResult<List<BannerOutput>> queryBannerByTypes(@RequestParam("type") List<Integer> type,@RequestParam("source") String source){
        List<Banner> banners = bannerService.selectListByTypes(BannerStatusEnum.NOMAL.getValue(), source,type);
        List<BannerOutput> bannerOutputs = bannerConverter.entityToOutput(banners);
        return ApiResult.ok(bannerOutputs);
    }

}
