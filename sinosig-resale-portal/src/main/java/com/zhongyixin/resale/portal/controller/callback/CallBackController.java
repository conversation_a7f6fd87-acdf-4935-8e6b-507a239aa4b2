package com.zhongyixin.resale.portal.controller.callback;

import com.zhongyixin.resale.biz.common.output.LianLianPayCallbackRespOutput;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.NotifyResponseOutput;
import com.zhongyixin.resale.biz.service.LianLianPayService;
import com.zhongyixin.resale.biz.service.LianlianPayNotifyService;
import com.zhongyixin.resale.portal.annotation.namespace.CallbackMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@CallbackMapping
public class CallBackController {


    @Autowired
    LianLianPayService lianLianPayService;

    @Autowired
    LianlianPayNotifyService lianlianPayNotifyService;

    @PostMapping("/lianlianpay/notify")
    public NotifyResponseOutput receiveLianlianpayNotify(@RequestBody PaymentNoticeInput notifyInput){
        log.info("lianlianpay NoticeInput:{}",notifyInput);
        return lianlianPayNotifyService.receiveLianlianPayNotify(notifyInput);
    }

    @PostMapping("/notifyMessage")
    public LianLianPayCallbackRespOutput receiveLianlianpayNotifyV2(@RequestBody PaymentNoticeInput notifyInput){
        log.info("lian lian pay callback message is {}",notifyInput);
        return lianLianPayService.disposeCallback(notifyInput);
    }
}
