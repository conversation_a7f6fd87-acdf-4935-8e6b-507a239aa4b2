package com.zhongyixin.resale.portal.converter;

import com.zhongyixin.resale.biz.common.output.BannerOutput;
import com.zhongyixin.resale.biz.entity.Banner;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2024/3/13 14:40
 */
@Mapper(componentModel = "spring")
public interface BannerConverter {

    BannerOutput entityToOutput(Banner banner);

    List<BannerOutput> entityToOutput(List<Banner> banner);


}
