package com.zhongyixin.resale.portal.controller;

import com.wftk.common.core.result.ApiResult;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.common.output.SunshineUserPointInfoQueryVO;
import com.zhongyixin.resale.biz.service.SunshineBusinessService;
import com.zhongyixin.resale.portal.annotation.namespace.PortalMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;



/**
 * <AUTHOR>
 * @create 2024/3/6 11:20
 */


@Slf4j
@Tag(name = "阳光业务api")
@PortalMapping("/sinosig")
public class SinosigBusinessController {


    @Autowired
    SunshineBusinessService sunshineBusinessService;

    @Operation(summary = "查询积分")
    @PostMapping("/querySinosigPoint")
    public ApiResult<SunshineUserPointInfoQueryVO> querySinosigPoint(@Validated @RequestBody SunshineUserInfoQueryDTO sunshineUserInfoQueryDTO){
        return sunshineBusinessService.querySinosigOrderPointLogicDeal(sunshineUserInfoQueryDTO);
    }

    @Operation(summary = "领取兑换")
    @PostMapping("/receiveCardRolls")
    public ApiResult<String> receiveCardRolls(@Validated @RequestBody SunshineReceiveCardRollsDTO sunshineReceiveCardRollsDTO){
        return ApiResult.ok(sunshineBusinessService.receiveCardRolls(sunshineReceiveCardRollsDTO));
    }



}
