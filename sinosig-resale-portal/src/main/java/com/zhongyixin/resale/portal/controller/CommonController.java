package com.zhongyixin.resale.portal.controller;

import com.wftk.common.core.result.ApiResult;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.common.output.SinosigUserInfoOutput;
import com.zhongyixin.resale.biz.entity.Issue;
import com.zhongyixin.resale.biz.entity.Protocol;
import com.zhongyixin.resale.biz.manage.CommonManager;
import com.zhongyixin.resale.biz.service.ProtocolService;
import com.zhongyixin.resale.portal.annotation.namespace.PortalMapping;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @create 2023/2/8 16:26
 */
@Slf4j
@Tag(name = "业务端-公共相关api")
@PortalMapping("/common")
public class CommonController {

    @Resource
    CommonManager commonManager;

    @Resource
    ProtocolService protocolService;

    @Operation(summary = "验签并解析参数")
    @PostMapping("/verifySignAndConvertParams")
    public ApiResult<SinosigUserInfoOutput> verifySignAndConvertParams(@RequestBody String jsonStr){
        return ApiResult.ok(commonManager.verifySignAndConvertParams(jsonStr));
    }

    @Operation(summary = "获取协议")
    @PostMapping("/getHTMLContent")
    public ApiResult<String> getHTMLContent(@RequestBody Protocol protocol){
        protocol.setStatus(Integer.parseInt(CommonConstant.Protocol.PUT_STATUS));
        return ApiResult.ok(protocolService.getHTMLContent(protocol));
    }

    @Operation(summary = "查询可用时间段")
    @GetMapping("/queryIssue")
    public ApiResult<Issue> queryIssue(@RequestParam("apiSource") String apiSource){
        Issue issue = commonManager.queryIssue(apiSource);
        return ApiResult.ok(issue);
    }




}
