package com.zhongyixin.resale.portal.schedule;

import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.entity.PayOrderLianlian;
import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.service.PayOrderLianlianService;
import com.zhongyixin.resale.biz.service.PaymentService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 连连支付定时任务
 * <AUTHOR>
 * @create 2023/2/15 11:26
 */
@Slf4j
@Component
public class LianlianPayJob {

    @Resource
    PayOrderLianlianService payOrderLianlianService;

    @Resource
    PaymentService paymentService;

    @Autowired
    LockManager lockManager;


    /**
     * cron:每10分钟执行一次
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void orderQueryAnHour() {
        //DLock
        DLock lock = lockManager.getLock(LianlianPayConstant.TIME_JOB_REDIS_KEY);

        try {
            if (!lock.tryLock()) {
                log.info("time job lian lian pay result lock get failed please ignore .");
                return;
            }
            List<PayOrderLianlian> payOrderLianlians = payOrderLianlianService.selectOrderByTenMinutes();
            if (!payOrderLianlians.isEmpty()) {
                for (PayOrderLianlian payOrderLianlian : payOrderLianlians) {
                    paymentService.orderQueryLianlianPayApi(payOrderLianlian);
                }
            }
        }finally {
            lock.unLock();
        }

    }





}
