package com.zhongyixin.resale.biz.config;

import com.wftk.http.client.core.executor.HttpRequestExecutor;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.DefaultLianlianPayApiManager;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.manage.factory.DefaultOrderProxyManagerFactory;
import com.zhongyixin.resale.biz.manage.factory.OrderProxyManagerFactory;
import com.zhongyixin.resale.biz.manage.pay.OrderProxyManager;
import com.zhongyixin.resale.biz.properties.LianlianPayQunqiuProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(LianlianPayQunqiuProperties.class)
public class LianlianPayConfiguration {

    @Bean(name = "lianlianPayQunqiuApiManager")
    LianlianPayApiManager lianlianPayApiManager(HttpRequestExecutor httpRequestExecutor, LianlianPayQunqiuProperties lianlianPayProperties){
        return new DefaultLianlianPayApiManager(httpRequestExecutor,lianlianPayProperties);
    }

    @Bean
    OrderProxyManagerFactory orderProxyManagerFactory(List<OrderProxyManager> list){
        return new DefaultOrderProxyManagerFactory(list);
    }
}
