package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.entity.LianLianPayCallbackRecord;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface LianLianPayCallbackRecordConverter {

    @Mapping(target = "oidPartner",source = "oid_partner")
    @Mapping(target = "signType",source = "sign_type")
    @Mapping(target = "sign",source = "sign")
    @Mapping(target = "noOrder",source = "no_order")
    @Mapping(target = "dtOrder",source = "dt_order")
    @Mapping(target = "moneyOrder",source = "money_order")
    @Mapping(target = "oidPaybill",source = "oid_paybill")
    @Mapping(target = "infoOrder",source = "info_order")
    @Mapping(target = "resultPay",source = "result_pay")
    @Mapping(target = "settleDate",source = "settle_date")
    LianLianPayCallbackRecord inputToRecord(PaymentNoticeInput paymentNoticeInput);

}
