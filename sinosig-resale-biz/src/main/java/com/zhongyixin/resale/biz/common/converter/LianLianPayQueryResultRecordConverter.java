package com.zhongyixin.resale.biz.common.converter;

import com.zhongyixin.resale.biz.entity.LianLianPayQueryResultRecord;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.QueryPaymentOut;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface LianLianPayQueryResultRecordConverter {

    @Mapping(target = "retCode",source = "ret_code")
    @Mapping(target = "retMsg",source = "ret_msg")
    @Mapping(target = "noOrder",source = "no_order")
    @Mapping(target = "oidPartner",source = "oid_partner")
    @Mapping(target = "oidPaybill",source = "oid_paybill")
    @Mapping(target = "confirmCode",source = "confirm_code")
    @Mapping(target = "signType",source = "sign_type")
    @Mapping(target = "sign",source = "sign")
    LianLianPayQueryResultRecord payOutToResultRecord(PaymentOut paymentOut);

    @Mapping(target = "retCode",source = "ret_code")
    @Mapping(target = "retMsg",source = "ret_msg")
    @Mapping(target = "noOrder",source = "no_order")
    @Mapping(target = "oidPartner",source = "oid_partner")
    @Mapping(target = "oidPaybill",source = "oid_paybill")
    @Mapping(target = "moneyOrder",source = "money_order")
    @Mapping(target = "resultPay",source = "result_pay")
    @Mapping(target = "settleDate",source = "settle_date")
//    @Mapping(target = "infoOrder",source = "info_order")
    @Mapping(target = "dtOrder",source = "dt_order")
    @Mapping(target = "memo",source = "memo")
    @Mapping(target = "signType",source = "sign_type")
    @Mapping(target = "sign",source = "sign")
    LianLianPayQueryResultRecord queryPaymentToLianLianRecord(QueryPaymentOut queryPaymentOut);

}
