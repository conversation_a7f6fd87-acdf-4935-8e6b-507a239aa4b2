package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.converter.LianLianPayCallbackRecordConverter;
import com.zhongyixin.resale.biz.entity.LianLianPayCallbackRecord;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import com.zhongyixin.resale.biz.mapper.LianLianPayCallbackRecordMapper;
import com.zhongyixin.resale.biz.service.LianLianPayCallbackRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-02
 */
@Service
@Slf4j
public class LianLianPayCallbackRecordServiceImpl extends ServiceImpl<LianLianPayCallbackRecordMapper, LianLianPayCallbackRecord> implements LianLianPayCallbackRecordService {


    @Autowired
    LianLianPayCallbackRecordConverter lianLianPayCallbackRecordConverter;

    @Override
    public void save(PaymentNoticeInput paymentNoticeInput) {
        LianLianPayCallbackRecord lianLianPayCallbackRecord = lianLianPayCallbackRecordConverter.inputToRecord(paymentNoticeInput);
        save(lianLianPayCallbackRecord);
    }
}
