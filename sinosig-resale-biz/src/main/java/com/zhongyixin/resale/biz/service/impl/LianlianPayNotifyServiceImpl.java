package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;
import com.zhongyixin.resale.biz.common.constant.*;
import com.zhongyixin.resale.biz.common.event.QuotaEvent;
import com.zhongyixin.resale.biz.entity.*;
import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.PaymentStatusEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.RetCodeEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.NotifyResponseOutput;
import com.zhongyixin.resale.biz.manage.SunshineOrderManager;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LianlianPayNotifyServiceImpl implements LianlianPayNotifyService {

    @Resource
    LianlianPayApiManager lianlianPayApiManager;

    @Resource
    PayOrderLianlianService payOrderLianlianService;

    @Resource
    ServiceOrderService serviceOrderService;

    @Resource
    IntefaceInfoService intefaceInfoService;

    @Resource
    PaymentService paymentService;

    @Resource
    OrderService orderService;

    @Resource
    PayRecordService payRecordService;

    @Resource
    SunshineOrderManager sunshineOrderManager;

    @Resource
    ApplicationContext applicationContext;

    @Resource
    VasOrderPublishRecordService vasOrderPublishRecordService;


    /**
     * 接收连连支付通知并处理逻辑
     * @param paymentNoticeInput
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public NotifyResponseOutput receiveLianlianPayNotify(PaymentNoticeInput paymentNoticeInput) {
        Boolean signCheck = lianlianPayApiManager.verifySign(JSONObject.getInstance().toJSONString(paymentNoticeInput) ,paymentNoticeInput.getSign() );
        NotifyResponseOutput responseBean = new NotifyResponseOutput();
        if (!signCheck) {
            // 传送数据被篡改，可抛出异常，再人为介入检查原因
            log.error("lianlianpay [receicePaymentNotify] 返回结果验签异常,可能数据被篡改,接收参数为：{}",paymentNoticeInput);
            // 回调内容先验签，再处理相应逻辑
            responseBean.setRet_code(RetCodeEnum.SYSTEM_ERROR.getCode());
            responseBean.setRet_msg(RetCodeEnum.SYSTEM_ERROR.getMsg());
            return responseBean;
        }

        PayOrderLianlian payOrderLianlian = payOrderLianlianService.selectPayOrderLianlianByPayOrderId(paymentNoticeInput.getNo_order());
        if(payOrderLianlian == null){
            log.info("lianlianpay [receicePaymentNotify] 支付订单号不存在,支付订单号：{}",paymentNoticeInput.getNo_order());
            responseBean.setRet_code(RetCodeEnum.SUCC.getCode());
            responseBean.setRet_msg(RetCodeEnum.SUCC.getMsg());
            return responseBean;
        }
        if (paymentNoticeInput.getResult_pay().equals(PaymentStatusEnum.PAYMENT_SUCCESS.getValue())) {
            //  商户更新订单为成功，处理自己的业务逻辑
            //成功
            LocalDateTime tranDate = LocalDateTime.now();
            //修改订单为成功
            Order order = new Order();
            order.setTranDate(tranDate);
            order.setPaymentNo(payOrderLianlian.getPayOrderId());
            order.setStatus(Integer.parseInt(OrderConstant.SUCCESS_ORDER_STATUS));
            order.setOrderId(payOrderLianlian.getOrderId());
            Integer integer = orderService.updateOrderByOrderId(order);
            if (integer < 1){
                responseBean.setRet_code(RetCodeEnum.SUCC.getCode());
                responseBean.setRet_msg(RetCodeEnum.SUCC.getMsg());
                return responseBean;
            }

            Order orderByOrderId = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());
            ServiceOrder serviceOrderInDB = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());

            //TODO 怎么调整
//            if(yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())){
            if(true){
                //修改阳光订单状态
                ServiceOrder serviceOrder = new ServiceOrder();
                serviceOrder.setOrderId(orderByOrderId.getCardOrderNo());
                serviceOrder.setPaymentNo(paymentNoticeInput.getNo_order());
                serviceOrder.setPayCompany(orderByOrderId.getPayCompany());
                serviceOrder.setTranDate(tranDate);
                // 获取起息日
                LocalDate startRateDate = tranDate.toLocalDate();
                if(StrUtil.isNotBlank(serviceOrderInDB.getAgentCode())){
                    // 代理订单 起息日为T+2月的1号
                    startRateDate = startRateDate.plusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
                }
                serviceOrder.setStartRateDate(startRateDate);
                serviceOrder.setStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED));
                serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED));
                serviceOrder.setSunshineServiceCharge(ServiceOrderConstant.Charge.SERVICECHARGE);
                serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
            }


            //修改连连订单
            payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.SUCCESS_ORDER_STATUS));
            String settleDate = paymentNoticeInput.getSettle_date();
            payOrderLianlian.setSettleDate(settleDate);
            payOrderLianlian.setPaymentOrderNo(paymentNoticeInput.getOid_paybill());
            payOrderLianlian.setInfoOrder(paymentNoticeInput.getInfo_order());
            payOrderLianlian.setMemo(LianlianPayConstant.MEMO);
            payOrderLianlianService.updatePayOrderLianlianByPayOrderId(payOrderLianlian);

            //修改记录
            VasOrderPublishRecord vasOrderPublishRecord = vasOrderPublishRecordService.getByChildOrderNo(String.valueOf(payOrderLianlian.getId()));
            if(vasOrderPublishRecord != null){
                //银行卡没有做数据补偿，所以要判空
                vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.SUCCESS);
                vasOrderPublishRecord.setExternalOrderNo(paymentNoticeInput.getNo_order());
                vasOrderPublishRecord.setPushSuccessTime(LocalDateTime.now());
                vasOrderPublishRecordService.updateById(vasOrderPublishRecord);
            }

            payRecordService.insertPayRecord(orderByOrderId,payOrderLianlian.getStatus(),
                    payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY,"交易成功");

            //修改响应信息
            IntefaceInfo intefaceInfo = new IntefaceInfo();
            intefaceInfo.setRequestId(paymentNoticeInput.getNo_order());
            intefaceInfo.setResponseMsg(JSONObject.getInstance().toJSONString(paymentNoticeInput));
            intefaceInfoService.updateIntefaceInfoByRequestId(intefaceInfo);

            paymentService.paymentSuccessDealLogic(orderByOrderId,tranDate);

            //TODO 怎么调整
//            if(yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())){
            if(true){

                    //扣减额度
                sunshineOrderManager.organizeDeductionAmountLogic(orderByOrderId,serviceOrderInDB,payOrderLianlian.getPayOrderId());

                // 国金核销通知
                //TODO 请确认是否需要
                try {
//                    guojinBusinessService.sunshineOrderVerificationForGuojin(serviceOrderInDB,tranDate);
                } catch (Exception e) {
                    log.info("lianlianpay querypayment 国金核销阳光订单失败", e);
                }

                // 发布事件通知
                try{
                    applicationContext.publishEvent(new QuotaEvent(serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo()).getOrgParentCode()));
                }catch (Exception e){
                    log.info("lianlianpay notify 发布事件失败",e);
                }
            }

        } else if (paymentNoticeInput.getResult_pay().equals(PaymentStatusEnum.PAYMENT_FAILURE.getValue())) {
            //  商户更新订单为失败，处理自己的业务逻辑
            //失败
            //修改订单为失败
            Order order = new Order();
            order.setTranDate(LocalDateTime.now());
            order.setPaymentNo(payOrderLianlian.getPayOrderId());
            order.setFailExplain(paymentNoticeInput.getInfo_order());
            order.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
            order.setOrderId(payOrderLianlian.getOrderId());
            Integer integer = orderService.updateOrderByOrderId(order);
            if (integer < 1){
                responseBean.setRet_code(RetCodeEnum.SUCC.getCode());
                responseBean.setRet_msg(RetCodeEnum.SUCC.getMsg());
                return responseBean;
            }

            Order orderByOrderId = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());

            //TODO 怎么调整
//            if(yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())){
            if(true){

                    //修改阳光订单状态
                ServiceOrder serviceOrder = new ServiceOrder();
                serviceOrder.setOrderId(payOrderLianlian.getCardOrderNo());
                serviceOrder.setStatus(NumberUtil.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED));
                serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
            }


            //修改连连订单
            payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
            payOrderLianlian.setPaymentOrderNo(paymentNoticeInput.getOid_paybill());
            payOrderLianlian.setInfoOrder(paymentNoticeInput.getInfo_order());
            payOrderLianlian.setMemo(LianlianPayConstant.MEMO);
            payOrderLianlianService.updatePayOrderLianlianByPayOrderId(payOrderLianlian);

            //修改记录
            VasOrderPublishRecord vasOrderPublishRecord = vasOrderPublishRecordService.getByChildOrderNo(String.valueOf(payOrderLianlian.getId()));
            if(vasOrderPublishRecord != null){
                vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.FAIL);
                vasOrderPublishRecord.setRemark(paymentNoticeInput.getInfo_order());
                vasOrderPublishRecordService.updateById(vasOrderPublishRecord);
            }

            String  failMsg = paymentNoticeInput.getInfo_order().replace(LianlianPayConstant.QUNQIU_INFO_ORDER,"");
            if(StringUtils.isNotBlank(failMsg)){
                failMsg = failMsg.replace(CommonConstant.UNDERLINE, "");
            }

            payRecordService.insertPayRecord(orderByOrderId,payOrderLianlian.getStatus(),
                    payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY,failMsg);

            //TODO 怎么调整
//            if(yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())){
            if(true){

                    //恢复额度
                ServiceOrder serviceOrderByOrderId = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());
                sunshineOrderManager.organizeRecoveryQuotaLogic(orderByOrderId,serviceOrderByOrderId,payOrderLianlian.getPayOrderId());
            }

            //修改响应信息
            IntefaceInfo intefaceInfo = new IntefaceInfo();
            intefaceInfo.setRequestId(paymentNoticeInput.getNo_order());
            intefaceInfo.setResponseMsg(JSONObject.getInstance().toJSONString(paymentNoticeInput));
            intefaceInfoService.updateIntefaceInfoByRequestId(intefaceInfo);

        } else {
            //  返回订单为退款状态 ，商户可以更新订单为失败或者退款
            // 退款这种情况是极小概率情况下才会发生的，个别银行处理机制是先扣款后再打款给用户时，
            // 才检验卡号姓名信息的有效性，当卡号姓名信息有误发生退款，实际上钱没打款到商户。
            // 这种情况商户代码上也可不做考虑，如发生用户投诉未收到钱，可直接联系连连客服，连连会跟银行核对
            // 退款情况，异步通知会通知两次，先通知成功，后通知退款（极小概率情况下才会发生的）
            log.error("lianlianpay payNotify refund ["+paymentNoticeInput.getNo_order()+"]");
        }

        responseBean.setRet_code(RetCodeEnum.SUCC.getCode());
        responseBean.setRet_msg(RetCodeEnum.SUCC.getMsg());
        return responseBean;
    }
}
