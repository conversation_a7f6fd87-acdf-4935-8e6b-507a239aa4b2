package com.zhongyixin.resale.biz.common.handler.order;


import com.zhongyixin.resale.biz.common.event.OrderProxyPayFailEvent;
import com.zhongyixin.resale.biz.common.event.base.OrderProxyEvent;
import com.zhongyixin.resale.biz.common.handler.order.base.OrderProxyEventAbstractHandler;
import com.zhongyixin.resale.biz.entity.OrderProxy;
import com.zhongyixin.resale.biz.manage.factory.OrderProxyManagerFactory;
import com.zhongyixin.resale.biz.manage.pay.OrderProxyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class OrderProxyPayFailEventHandler extends OrderProxyEventAbstractHandler {

    @Autowired
    OrderProxyManagerFactory orderProxyManagerFactory;

    @Override
    public void handler(OrderProxyEvent orderProxyEvent) {
        OrderProxy orderProxy = orderProxyEvent.getOrderProxy();
        OrderProxyManager orderProxyManager = orderProxyManagerFactory.get(orderProxy.getPayChannel());
        if (orderProxyManager == null) {
            return;
        }
        orderProxyManager.sendNotify(orderProxyEvent.getOrderProxy());
    }

    @Override
    public boolean support(OrderProxyEvent orderProxyEvent) {
        return orderProxyEvent instanceof OrderProxyPayFailEvent;
    }

}
