package com.zhongyixin.resale.biz.ext.sinosig.output.result;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024/3/6 15:17
 */
@Data
public class SinosigResult {

    private static String successCode = "0000";
    private static String successDesc = "成功";

    private String resultCode;
    private String resultDesc;

    public static SinosigResult fail(String resultCode, String resultDesc){
        SinosigResult result = new SinosigResult();
        result.resultCode = resultCode;
        result.resultDesc = resultDesc;
        return result;
    }


    public static SinosigResult ok(){
        SinosigResult result = new SinosigResult();
        result.resultCode = successCode;
        result.resultDesc = successDesc;
        return result;
    }



}
