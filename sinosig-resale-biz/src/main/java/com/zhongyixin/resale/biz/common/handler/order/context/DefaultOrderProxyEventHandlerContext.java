package com.zhongyixin.resale.biz.common.handler.order.context;

import cn.hutool.core.collection.CollectionUtil;
import com.zhongyixin.resale.biz.common.event.base.OrderProxyEvent;
import com.zhongyixin.resale.biz.common.handler.order.base.OrderProxyEventAbstractHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class DefaultOrderProxyEventHandlerContext implements OrderProxyEventHandlerContext {

    private final List<OrderProxyEventAbstractHandler> list;

    public DefaultOrderProxyEventHandlerContext(List<OrderProxyEventAbstractHandler> list) {
        this.list = list;
    }


    @Override
    public void handleEvent(OrderProxyEvent orderProxyEvent) {
        if (CollectionUtil.isEmpty(list)) {
            log.warn("handler is empty");
            return;
        }
        for (OrderProxyEventAbstractHandler handler : list) {
            if (handler.support(orderProxyEvent)) {
                handler.handler(orderProxyEvent);
                return;
            }
        }
        log.warn("handler is null");
    }


}
