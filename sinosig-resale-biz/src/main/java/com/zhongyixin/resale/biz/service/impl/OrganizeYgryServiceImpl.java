package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.constant.YuJiaConstant;
import com.zhongyixin.resale.biz.common.input.OrganizeBalanceOperateDTO;
import com.zhongyixin.resale.biz.common.util.SunshineOrgannizeUtil;
import com.zhongyixin.resale.biz.entity.*;
import com.zhongyixin.resale.biz.mapper.OrganizeYgryMapper;
import com.zhongyixin.resale.biz.service.SysDeptService;
import com.zhongyixin.resale.biz.service.OrganizeYgryService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 阳光机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Slf4j
@Service
public class OrganizeYgryServiceImpl extends ServiceImpl<OrganizeYgryMapper, OrganizeYgry> implements OrganizeYgryService {

    @Resource
    OrganizeYgryMapper organizeYgryMapper;

    @Autowired
    SysDeptService sysDeptService;


    @Override
    public OrganizeYgry selectOrganize(String orgCode) {
        return organizeYgryMapper.selectOrganize(orgCode);
    }

    @Override
    public void createOrganize(ServiceOrder serviceOrder, String orgName) {
        OrganizeYgry organizeYgry = organizeYgryMapper.selectOrganize(serviceOrder.getOrgCode());
        if (organizeYgry == null) {
            String orgCode = serviceOrder.getOrgCode();
            //为空添加机构并判断是否是二级机构
            Boolean isSecondOrganize = SunshineOrgannizeUtil.isSecondOrganize(orgCode);

            organizeYgry = new OrganizeYgry();
            organizeYgry.setOrgCode(orgCode);
            organizeYgry.setOrgName(orgName);
            organizeYgry.setOrgParentCode(isSecondOrganize ? null : serviceOrder.getOrgParentCode());
            organizeYgry.setLevel(isSecondOrganize ? 2 : 3);
            organizeYgry.setCreateTime(LocalDateTime.now());

            if (!isSecondOrganize) {
                //如果为三级机构 判断他的父级是否为空
                OrganizeYgry secondOrganize = organizeYgryMapper.selectOrganize(organizeYgry.getOrgParentCode());
                if (secondOrganize == null) {
                    String secondOrgName = getOrgName(organizeYgry);
                    secondOrganize = new OrganizeYgry();
                    secondOrganize.setOrgCode(organizeYgry.getOrgParentCode());
                    secondOrganize.setOrgName(secondOrgName);
                    secondOrganize.setOrgParentCode(null);
                    secondOrganize.setLevel(2);
                    secondOrganize.setCreateTime(LocalDateTime.now());
                    organizeYgryMapper.insert(secondOrganize);

                    SysDept sysDept = new SysDept();
                    sysDept.setAncestors(organizeYgry.getOrgCode());
                    sysDept.setCreateTime(LocalDateTime.now());
                    sysDept.setCreateBy("admin");
                    sysDept.setDeptName(organizeYgry.getOrgName());
                    sysDept.setStatus(0);
                    sysDept.setDelFlag(0);
                    sysDept.setOrderNum(1);
                    sysDept.setParentId(0);
                    sysDept.setRemark("RONGZHI");
                    sysDeptService.save(sysDept);
                    log.info("sysDept save success. {}",sysDept);
                }else {
                    // 默认为二级开关
                    organizeYgry.setAlipaySwitch(secondOrganize.getAlipaySwitch());
                }
            }
            organizeYgryMapper.insert(organizeYgry);
            log.info("organize save success. {}",organizeYgry);

            // 创建部门以及为二级机构添加权限值
            SysDept sysDept = new SysDept();
            sysDept.setAncestors(organizeYgry.getOrgCode());
            sysDept.setCreateTime(LocalDateTime.now());
            sysDept.setCreateBy("admin");
            sysDept.setDeptName(organizeYgry.getOrgName());
            sysDept.setStatus(0);
            sysDept.setDelFlag(0);
            sysDept.setOrderNum(1);
            if(!isSecondOrganize){
                OrganizeYgry secondOrganize = organizeYgryMapper.selectOrganize(organizeYgry.getOrgParentCode());
                SysDept deptByDeptName = sysDeptService.findDeptByDeptName(secondOrganize.getOrgName());
                sysDept.setParentId(deptByDeptName.getDeptId());
            }else {
                sysDept.setParentId(0);
            }
            sysDept.setRemark("RONGZHI");
            sysDeptService.save(sysDept);
            log.info("sysDept save success. {}",sysDept);
            if(!isSecondOrganize){
                sysDeptService.changeSecondOrgAncestors(organizeYgry.getOrgParentCode());
                log.info("sysDept ancestors change success. {}",sysDept);
            }
        }
    }

    @Override
    public Integer updatePreserveStatus(OrganizeYgry organizeYgry) {
        return organizeYgryMapper.updatePreserveStatus(organizeYgry);
    }

    /**
     * 扣减金额
     *
     * @param organizeBalanceOperateDTO
     * @return
     */
    @Override
    public Integer deductionAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO) {
        return organizeYgryMapper.deductionAmount(organizeBalanceOperateDTO);
    }

    /**
     * 恢复金额
     *
     * @param organizeBalanceOperateDTO
     * @return
     */
    @Override
    public Integer recoveryAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO) {
        return organizeYgryMapper.recoveryAmount(organizeBalanceOperateDTO);
    }

    /**
     * 冻结金额
     *
     * @param organizeBalanceOperateDTO
     * @return
     */
    @Override
    public Integer frozenAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO) {
        return organizeYgryMapper.frozenAmount(organizeBalanceOperateDTO);
    }

    private String getOrgName(OrganizeYgry organizeYgry) {
        String orgName = organizeYgry.getOrgName().substring(0, organizeYgry.getOrgName().indexOf("分公司"));
        if (organizeYgry.getOrgName().contains(YuJiaConstant.PROVINCE)) {
            orgName = organizeYgry.getOrgName().replace(YuJiaConstant.PROVINCE, "");
        } else if (organizeYgry.getOrgName().contains(YuJiaConstant.MUNICIPALITY)) {
            orgName = organizeYgry.getOrgName().replace(YuJiaConstant.MUNICIPALITY, "");
        } else if (organizeYgry.getOrgName().contains(YuJiaConstant.CITY)) {
            orgName = organizeYgry.getOrgName().replace(YuJiaConstant.CITY, "");
        }
        return orgName;
    }
}
