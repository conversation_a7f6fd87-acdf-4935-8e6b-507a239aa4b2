package com.zhongyixin.resale.biz.entity;

import com.zhongyixin.resale.biz.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 *  代付订单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderProxy extends BaseEntity {


    private static final long serialVersionUID = 6185338877506551990L;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 业务流水号
     */
    private String serialNo;

    /**
     * 收款人账号
     */
    private String payeeAccount;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 金额
     */
    private String amount;

    /**
     * 订单来源。例如：亿联、阳光等
     */
    private String platformCode;

    /**
     * 代付渠道编号。民生、连连、拉卡拉
     */
    private String payChannel;

    /**
     * 订单状态：0：处理中；1：成功：2：失败
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 代付渠道的返回结果码，不同代付渠道，结果码不一样
     */
    private String retCode;

    /**
     * 交易时间
     */
    private LocalDateTime tradeDate;

    /**
     * 交易完成时间
     */
    private LocalDateTime tradeFinishDate;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 对公对私标志。0 - 对私。1 - 对公。
     */
    private String flagCard;

    /**
     * 开户行所在省市编码
     */
    private String cityCode;

    /**
     * 开户支行名称 对公字段
     */
    private String brabankName;

    /**
     * 开户支行行号 对公字段
     */
    private String brabankNum;

}
