package com.zhongyixin.resale.biz.common.enums;

/**
 * <AUTHOR>
 */
public enum DataScopeEnum {

    ALL(1, "全部数据"),
    PROVIDER_DATA(2, "服务商数据"),
    SUNSHINE_DATA(3, "阳光机构数据");

    private final int code;
    private final String info;

    DataScopeEnum(int code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public int getCode()
    {
        return code;
    }


    public String getInfo()
    {
        return info;
    }
}
