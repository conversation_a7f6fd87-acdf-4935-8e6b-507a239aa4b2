package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.OrderProxy;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface OrderProxyMapper extends BaseMapper<OrderProxy> {

    /**
     * 根据订单状态、来源和渠道获取订单
     * @param status
     * @param platformCode
     * @param payChannel
     * @return
     */
    List<OrderProxy> getOrdersByStatus(@Param("status") Integer status,
                                       @Param("platformCode") String platformCode,
                                       @Param("payChannel") String payChannel,
                                       @Param("dateTime") Date dateTime);

    /**
     * 根据平台编码、流水号获取代付订单
     * @param platformCode
     * @param serialNo
     * @return
     */
    OrderProxy getOrderBySerialNoAndPlfmCode(@Param("platformCode") String platformCode,
                                             @Param("serialNo") String serialNo);

    /**
     * 根据订单号获取订单
     * @param orderNo
     * @return
     */
    OrderProxy getByOrderNo(@Param("orderNo") String orderNo);

    List<OrderProxy> batchQueryOrderProxys(@Param("platformCode") String platformCode,
                                           @Param("serialNos") List<String> serialNos);
}
