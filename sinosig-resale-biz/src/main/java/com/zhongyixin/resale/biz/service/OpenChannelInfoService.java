package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.OpenChannelInfo;

import java.util.List;

/**
 * <p>
 * 开放渠道信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface OpenChannelInfoService extends IService<OpenChannelInfo> {

    OpenChannelInfo selectOneByChannelCode(String channelCode, Integer enabled);

    /**
     * 连连专用 注意其他的通道不要用这个方法！！！！！！！！！！！
     * @param channelCode
     * @param enabled
     * @return
     */
    @Deprecated
    List<OpenChannelInfo> fuzzyFindByChannelCode(String channelCode, Integer enabled);

}
