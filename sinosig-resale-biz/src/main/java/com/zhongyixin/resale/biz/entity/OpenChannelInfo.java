package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 开放渠道信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OpenChannelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    private Integer deleted;

    /**
     * 开关
     */
    private Integer enabled;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 配置参数
     */
    private String channelParams;

    /**
     * 主体名称
     */
    private String providerName;

    /**
     * 描述
     */
    private String description;


}
