package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;

import java.util.List;

/**
 * <p>
 * 总订单表 服务类
 * </p>
 * <AUTHOR>
 * @since 2023-02-08
 */
public interface OrderService extends IService<Order> {


    Order selectOrderByCardOrderNo(String orderId);

    Order selectOrderByApiSource(Order order);

    /**
     * 根据orderId查询订单
     * @param orderId
     * @return
     */
    Order selectOrderByOrderId(String orderId);

    /**
     * 修改订单
     * @param dto
     * @return
     */
    boolean updateOrder(UpdateOrderDTO dto);

    /**
     * 根据id修改订单
     * 状态为初始化或失败
     * @param order
     * @return
     */
    Integer updateOrderById(Order order);

    /**
     * 根据id修改订单
     * 状态为处理中
     * @param order
     * @return
     */
    Integer updateOrderByIdAndDealing(Order order);

    /**
     * 根据订单id修改订单
     * 状态为处理中
     * @param order
     * @return
     */
    Integer updateOrderByOrderId(Order order);

    Order createOrderByServiceOrder(ServiceOrder serviceOrder);




    List<Order> getOrderListByStatus(Integer status);

    /**
     * 取消订单
     * @param orderId
     * @return
     */
    boolean cancelOrder(String orderId);


}
