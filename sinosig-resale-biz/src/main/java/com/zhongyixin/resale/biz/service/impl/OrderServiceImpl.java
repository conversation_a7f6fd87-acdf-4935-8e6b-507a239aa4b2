package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wftk.exception.core.exception.BusinessException;
import com.zhongyixin.resale.biz.common.constant.OrderConstant;
import com.zhongyixin.resale.biz.common.converter.SinosigDtoConvert;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.manage.CommonManager;
import com.zhongyixin.resale.biz.mapper.OrderMapper;
import com.zhongyixin.resale.biz.service.OrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 总订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    @Resource
    OrderMapper orderMapper;


    @Override
    public Order selectOrderByCardOrderNo(String orderId) {
        return orderMapper.selectOrderByCardOrderNo(orderId);
    }

    @Override
    public Order selectOrderByApiSource(Order order) {
        return orderMapper.selectOrderByApiSource(order);
    }

    /**
     * 根据orderId查询订单
     * @param orderId
     * @return
     */
    @Override
    public Order selectOrderByOrderId(String orderId) {
        return orderMapper.selectOrderByOrderId(orderId);
    }


    @Override
    public boolean updateOrder(UpdateOrderDTO dto) {
        return baseMapper.updateOrder(dto) > 0;
    }

    @Override
    public Integer updateOrderById(Order order) {
        return orderMapper.updateOrderById(order);
    }

    /**
     * 根据id修改订单
     * 状态为处理中
     * @param order
     * @return
     */
    @Override
    public Integer updateOrderByIdAndDealing(Order order) {
        return orderMapper.updateOrderByIdAndDealing(order);
    }

    /**
     * 根据订单id修改订单
     * 状态为处理中
     * @param order
     * @return
     */
    @Override
    public Integer updateOrderByOrderId(Order order) {
        return orderMapper.updateOrderByOrderId(order);
    }

    @Override
    @Transactional(noRollbackFor = BusinessException.class)
    public Order createOrderByServiceOrder(ServiceOrder serviceOrder) {
        final Order order = SinosigDtoConvert.INSTANCE.serviceOrderToOrder(serviceOrder);

        //检测Order是否存在
        Order existedOrder = selectOrderByApiSource(order);
        if (existedOrder == null) {
            existedOrder = SinosigDtoConvert.INSTANCE.serviceOrderToOrder(serviceOrder);
            existedOrder.setOrderId(IdUtil.getSnowflakeNextIdStr());
            existedOrder.setStatus(Integer.parseInt(OrderConstant.INIT_ORDER_STATUS));
            existedOrder.setOrderType(OrderConstant.OrderType.NEW_YGBH);
            //新事务写入
            boolean savedOrder = save(existedOrder);
            if (!savedOrder) {
                throw new RuntimeException("save order error. orderId: " + existedOrder.getOrderId());
            }
            log.info("Sunshine save order：{}", existedOrder);
        }
        return existedOrder;
    }

    @Override
    public List<Order> getOrderListByStatus(Integer status) {
        return orderMapper.selectListByStatus(status);
    }

    @Override
    public boolean cancelOrder(String orderId) {
        return baseMapper.cancelOrder(orderId) > 0;
    }


}
