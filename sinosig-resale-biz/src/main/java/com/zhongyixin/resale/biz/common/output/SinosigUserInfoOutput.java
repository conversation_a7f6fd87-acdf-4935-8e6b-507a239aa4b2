package com.zhongyixin.resale.biz.common.output;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023/2/8 17:39
 */
@Data
public class SinosigUserInfoOutput {

    /**
     * 客户来源
     */
    private String apiSource;

    /**
     * 路径跳转类型 (订单类型)
     */
    private String orderType;

    /**
     * 用户手机号码
     */
    private String telephone;

    /**
     * 用户姓名
     */
    private String customerNo;

    /**
     * 重定向路径
     */
    private String redirectUrl;

    /**
     * 卡密
     */
    @JsonProperty("card_code")
    private String cardCode;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 客户来源
     */
    private String source;
}
