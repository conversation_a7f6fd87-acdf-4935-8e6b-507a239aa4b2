package com.zhongyixin.resale.biz.entity;

import com.zhongyixin.resale.biz.entity.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单异常信息描述
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderProxyExceptionMsg extends BaseEntity {

    private static final long serialVersionUID = -4406429929182433134L;

    @Schema(description = "订单号（OrderProxy中的orderNo）")
    private String orderNo;

    @Schema(description = "描述信息")
    private String descriptionMsg;

    @Schema(description = "备注")
    private String remark;

}
