package com.zhongyixin.resale.biz.common.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class LianlianPayParamsDTO implements Serializable {
    private static final long serialVersionUID = 5621757144494670255L;

    /**
     * 订单描述。说明付款用途，5W以上必传。
     */
    private String infoOrder;

    /**
     * 0 - 对私。
     * 1 - 对公
     */
    private String flagCard;
    /**
     * 收款备注。 传递至银行， 一般作为订单摘要展示。
     */
    private String memo;

    /**
     * 接收异步通知的线上地址。
     */
    private String notifyUrl;

    /**
     * 商户编号是商户在连连支付支付平台上开设的商户号码，为18位数字
     */
    private String oidPartner;

    /**
     * 当前版本
     */
    private String apiVersion;

    /**
     * RSA
     */
    private String signType;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 主域名
     */
    private String host;

    /**
     * 卡bin查询主域名
     */
    private String cardBinhost;

    /**
     * 调用方的回调地址
     */
    private String callbackUrl;

}
