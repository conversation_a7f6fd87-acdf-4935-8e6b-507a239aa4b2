package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.entity.Banner;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
public interface BannerService extends IService<Banner> {

    List<Banner> selectListByType(Integer status, String apiSource, Integer type);

    List<Banner> selectListByTypes(Integer status, String apiSource, List<Integer> type);


    List<Banner> selectListByStatus(Integer status, String apiSource);

}
