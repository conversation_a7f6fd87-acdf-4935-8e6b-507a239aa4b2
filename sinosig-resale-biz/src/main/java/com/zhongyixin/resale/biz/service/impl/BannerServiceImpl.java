package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.Banner;
import com.zhongyixin.resale.biz.mapper.BannerMapper;
import com.zhongyixin.resale.biz.service.BannerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements BannerService {

    @Resource
    BannerMapper bannerMapper;

    @Override
    public List<Banner> selectListByType(Integer status, String apiSource, Integer type) {
        return bannerMapper.selectListByType(status,apiSource, type);
    }

    @Override
    public List<Banner> selectListByTypes(Integer status, String apiSource, List<Integer> type) {
        return bannerMapper.selectListByTypes(status,apiSource, type);
    }

    @Override
    public List<Banner> selectListByStatus(Integer status, String apiSource) {
        return bannerMapper.selectListByStatus(status,apiSource);
    }

}
