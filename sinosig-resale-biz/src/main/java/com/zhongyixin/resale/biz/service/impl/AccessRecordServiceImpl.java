package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.util.AddressUtils;
import com.zhongyixin.resale.biz.common.util.IpUtils;
import com.zhongyixin.resale.biz.common.util.ServletUtils;
import com.zhongyixin.resale.biz.entity.AccessRecord;
import com.zhongyixin.resale.biz.mapper.AccessRecordMapper;
import com.zhongyixin.resale.biz.service.AccessRecordService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户访问记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-31
 */
@Service
public class AccessRecordServiceImpl extends ServiceImpl<AccessRecordMapper, AccessRecord> implements AccessRecordService {

    @Override
    public void addAccessRecord(AccessRecord accessRecord) {
        accessRecord.setIpAddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        accessRecord.setDevice(ServletUtils.getRequest().getHeader("User-Agent"));
        accessRecord.setLocation(AddressUtils.getRealAddressByIp(accessRecord.getIpAddr()));
        accessRecord.setCreateTime(LocalDateTime.now());
        this.save(accessRecord);
    }
}
