package com.zhongyixin.resale.biz.manage;

import cn.hutool.core.util.StrUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.ChannelSettingConstant;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.common.output.SinosigUserInfoOutput;
import com.zhongyixin.resale.biz.entity.AccessRecord;
import com.zhongyixin.resale.biz.entity.ChannelSetting;
import com.zhongyixin.resale.biz.entity.Issue;
import com.zhongyixin.resale.biz.ext.sinosig.manage.SinosigApiManager;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 公共服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Service
@Slf4j
public class CommonManager {

    @Resource
    NotifyJobService notifyJobService;

    @Autowired
    private LockManager lockManager;

    @Resource
    ChannelSettingService channelSettingService;

    @Autowired
    SinosigApiManager sinosigApiManager;

    @Autowired
    AccessRecordService accessRecordService;



    /**
     * 验签以及转换参数
     *
     * @param jsonStr
     * @return
     */
    public SinosigUserInfoOutput verifySignAndConvertParams(String jsonStr) {
        JSONObject jsonObject = JSONObject.getInstance();
        Map<String, String> params = jsonObject.parseMap(jsonStr, String.class, String.class);
        // 记录用户访问详情
        AccessRecord accessRecord = new AccessRecord();
        try {
            //url 参数
            String param = params.get("params");
            String type = params.get("type");
            log.info("验签 type：{} params：{}", type, param);
            accessRecord.setType(type);
            byte[] decode = Base64.getDecoder().decode(param.replaceAll(" +", "+"));

            String jsonString = new String(decode);
            accessRecord.setParam(jsonString);
            Map<String, Object> paramsMap = jsonObject.parseMap(jsonString, String.class, Object.class);
            String apiSource = paramsMap.get("apiSource").toString();
            paramsMap = paramsMap.entrySet().stream().filter((e) -> e.getValue() != null && e.getValue() != "").collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue));

            accessRecord.setApiSource(apiSource);
            accessRecord.setIdentify(paramsMap.get("orderId").toString());

            boolean verifySign = sinosigApiManager.verifyInitialSortSign(paramsMap);
            if (!verifySign) {
                accessRecord.setStatus(CommonConstant.AccessRecord.STATUS_FAIL);
                accessRecord.setErrorInfo("非法签名");
                throw new BusinessException("非法签名");
            }

            paramsMap.put("orderType", type);

            return JSONObject.getInstance().convertValue(paramsMap, SinosigUserInfoOutput.class);
        } catch (Exception e) {
            accessRecord.setStatus(CommonConstant.AccessRecord.STATUS_FAIL);
            accessRecord.setErrorInfo(e.getMessage());
            throw e;
        } finally {
            accessRecordService.addAccessRecord(accessRecord);
        }
    }



    public Integer saveNotifyJob(String businessCode, String orderId, Object param) {
        // 加锁
        String key =  businessCode + orderId;
        String paramJsonStr = JSONObject.getInstance().toJSONString(param);
        DLock lock = lockManager.getCreateNotifyJobLock(key);
        try {
            if (lock.tryLock()) {
                return notifyJobService.saveNotifyJob(businessCode, orderId, paramJsonStr);
            }else{
                log.warn("save notifyJob wait timeout. businessCode: {}, orderId: {}, param: {}",
                        businessCode, orderId, paramJsonStr);
                throw new BusinessException("save notifyJob wait timeout");
            }
        } finally {
            lock.unLock();
        }
    }




    public Issue queryIssue(String apiSource) {
        if (StrUtil.isBlank(apiSource)) {
            log.error(apiSource + "渠道查询发布详情异常，apiSource为空");
            Issue issue = new Issue();
            issue.setStatus(CommonConstant.Issue.OFF);
            return issue;
        }

        Issue issue = new Issue();
        issue.setStatus(CommonConstant.Issue.OFF);
        return issue;
    }



}
