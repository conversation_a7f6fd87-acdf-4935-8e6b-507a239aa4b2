package com.zhongyixin.resale.biz.common.constant;

public interface OrderProxyConstant {

    /**
     * 0：处理中；1：成功：2：失败
     */
    interface STATUS {
        Integer PENDING = 0;
        Integer SUCCESS = 1;
        Integer FAIL = 2;
    }

    /**
     * 代付渠道编号。民生、连连、拉卡拉
     */
    interface CHANNEL {
        String MS = "cmbc";
        String LL = "lianlianpay";
        String LL_PUBLIC = "lianlianpay_public";
        String LKL = "lakala";
        String NEW_LKL = "new-lakala";
        String FUIOU = "fuiou";
    }


    /**
     * 订单来源平台。例如：亿联、阳光等
     */
    interface PLATFORM {
        String YL = "yillion";
    }

    /**
     * 对公对私标志。0 - 对私。1 - 对公。
     */
    interface FLAG_CARD{

        String PERSONAL_TRANSFER = "0";

        String PUBLIC_TRANSFER = "1";
    }

}
