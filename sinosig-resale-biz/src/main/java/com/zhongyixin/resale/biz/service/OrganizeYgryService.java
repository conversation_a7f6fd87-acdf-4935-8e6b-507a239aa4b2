package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.common.input.OrganizeBalanceOperateDTO;
import com.zhongyixin.resale.biz.entity.OrganizeYgry;
import com.zhongyixin.resale.biz.entity.ServiceOrder;


/**
 * <p>
 * 阳光机构表 服务类
 * </p>
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface OrganizeYgryService extends IService<OrganizeYgry> {


    OrganizeYgry selectOrganize(String orgCode);

    void createOrganize(ServiceOrder serviceOrder, String orgName);

    Integer updatePreserveStatus(OrganizeYgry organizeYgry);


    /**
     * 扣减金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer deductionAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    /**
     * 恢复金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer recoveryAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);

    /**
     * 冻结金额
     * @param organizeBalanceOperateDTO
     * @return
     */
    Integer frozenAmount(OrganizeBalanceOperateDTO organizeBalanceOperateDTO);
}
