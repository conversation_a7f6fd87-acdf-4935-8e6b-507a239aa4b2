package com.zhongyixin.resale.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.entity.OpenChannelInfo;
import com.zhongyixin.resale.biz.mapper.OpenChannelInfoMapper;
import com.zhongyixin.resale.biz.service.OpenChannelInfoService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 开放渠道信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class OpenChannelInfoServiceImpl extends ServiceImpl<OpenChannelInfoMapper, OpenChannelInfo> implements OpenChannelInfoService {

    @Resource
    OpenChannelInfoMapper openChannelInfoMapper;

    @Override
    public OpenChannelInfo selectOneByChannelCode(String channelCode, Integer enabled) {
        return openChannelInfoMapper.selectOneByChannelCode(channelCode,enabled);
    }

    @Override
    public List<OpenChannelInfo> fuzzyFindByChannelCode(String channelCode, Integer enabled) {
        return openChannelInfoMapper.fuzzyFindByChannelCode(channelCode, enabled);
    }


}
