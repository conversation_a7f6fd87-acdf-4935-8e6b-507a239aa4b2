package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.common.exception.OrganizeNoExistException;
import com.zhongyixin.resale.biz.common.input.ServiceOrderCopyDTO;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.OrganizeYgry;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.mapper.OrganizeYgryMapper;
import com.zhongyixin.resale.biz.mapper.ServiceOrderMapper;
import com.zhongyixin.resale.biz.service.ServiceOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <p>
 * 阳光明细订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service
@Slf4j
public class ServiceOrderServiceImpl extends ServiceImpl<ServiceOrderMapper, ServiceOrder> implements ServiceOrderService {


    @Autowired
    ServiceOrderMapper serviceOrderMapper;

    @Autowired
    OrganizeYgryMapper organizeYgryMapper;

    @Override
    public ServiceOrderCopyDTO selectServiceOrderCopyDTOByOrderId(String orderId) {
        return baseMapper.selectServiceOrderCopyDTOByOrderId(orderId);
    }

    /**
     * 查询订单
     *
     * @param orderId
     * @return
     */
    @Override
    public ServiceOrder selectServiceOrderByOrderId(String orderId) {
        return baseMapper.selectServiceOrderByOrderId(orderId);
    }
    @Override
    public boolean updateOrder(UpdateOrderDTO dto) {
        return baseMapper.updateOrder(dto) > 0;
    }

    @Override
    public Integer updateServiceOrderRepayStatus(ServiceOrder serviceOrder) {
        return baseMapper.updateServiceOrderRepayStatus(serviceOrder);
    }

    @Override
    public boolean cancelOrder(String orderId) {
        return baseMapper.cancelOrder(orderId) > 0;
    }


    /**
     * 根据父级机构编码、垫资状态获取超过订单周期的订单数目
     *
     * @param parentOrgCode
     * @return
     */
    @Override
    public int getOrderNumbserByOrg(String parentOrgCode) {
        LambdaQueryWrapper<OrganizeYgry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrganizeYgry::getOrgCode, parentOrgCode);
        queryWrapper.eq(OrganizeYgry::getStatus, 1);
        OrganizeYgry organizeYgry = organizeYgryMapper.selectOne(queryWrapper);
        if (organizeYgry == null) {
            throw new OrganizeNoExistException("organize: " + parentOrgCode + " not exist or disabled, please check this orgCode");
        }
        Integer quotaPeriod = organizeYgry.getQuotaPeriod();
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate startDate = today.minusDays(quotaPeriod);
        // 获取超期的订单数
        return serviceOrderMapper.getOrderNumbserByOrg(parentOrgCode, NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED), startDate);
    }
}
