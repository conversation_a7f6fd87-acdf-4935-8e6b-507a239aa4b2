package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.entity.OpenChannelInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 开放渠道信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface OpenChannelInfoMapper extends BaseMapper<OpenChannelInfo> {


    OpenChannelInfo selectOneByChannelCode(@Param("channelCode") String channelCode,@Param("enabled")Integer enabled);

    /**
     * 连连专用 注意其他的通道不要用这个方法！！！！！！！！！！！
     * @param channelCode
     * @param enabled
     * @return
     */
    @Deprecated
    List<OpenChannelInfo> fuzzyFindByChannelCode(@Param("channelCode") String channelCode, @Param("enabled")Integer enabled);

}
