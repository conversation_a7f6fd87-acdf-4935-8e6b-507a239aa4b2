package com.zhongyixin.resale.biz.entity;

import com.zhongyixin.resale.biz.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  连连付款订单主动结果查询记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LianLianPayQueryResultRecord extends BaseEntity {


    private static final long serialVersionUID = 5054266571214083710L;

    /**
     * 结果码
     */
    private String retCode;

    /**
     * 结果信息
     */
    private String retMsg;

    /**
     * 原请求中商户订单号
     */
    private String noOrder;

    /**
     * 商户编号是商户在连连支付支付平台上开设的商户号码
     */
    private String oidPartner;

    /**
     * 连连订单创建成功时返回
连连支付单号。 全局唯一
     */
    private String oidPaybill;

    /**
     * ret_code 为4002, 4003, 4004 时返回
     */
    private String confirmCode;

    /**
     * RSA
     */
    private String signType;

    /**
     * 签名
     */
    private String sign;

    /**
     * 商户订单时间
     */
    private String dtOrder;

    /**
     * 原请求中交易金额。
     */
    private String moneyOrder;

    /**
     * APPLY， 付款申请。
CHECK， 复核申请。
SUCCESS， 成功。
PROCESSING，付款处理中。
CANCEL，付款退款， 付款成功后，有可能发生退款。
FAILURE， 失败 。
CLOSED， 付款关闭。
     */
    private String resultPay;

    /**
     * 清算日期
     */
    private String settleDate;

    /**
     * 付款失败的原因
     */
    private String memo;

    /**
     * 1、请求结果；2、查询结果
     */
    private Integer type;

}
