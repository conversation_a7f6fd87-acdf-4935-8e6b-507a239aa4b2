package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.exception.core.exception.BusinessException;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.*;
import com.zhongyixin.resale.biz.common.event.QuotaEvent;
import com.zhongyixin.resale.biz.common.exception.OrganizeExceedTimeOrderException;
import com.zhongyixin.resale.biz.common.exception.OrganizeNoExistException;
import com.zhongyixin.resale.biz.common.exception.OrganizeNotSufficientFundsException;
import com.zhongyixin.resale.biz.entity.*;
import com.zhongyixin.resale.biz.ext.lianlianpay.constant.LianlianPayConstant;
import com.zhongyixin.resale.biz.ext.lianlianpay.dto.PaymentDTO;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.PaymentStatusEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.RetCodeEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.manager.LianlianPayApiManager;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.PaymentOut;
import com.zhongyixin.resale.biz.ext.lianlianpay.output.QueryPaymentOut;
import com.zhongyixin.resale.biz.manage.SunshineOrderManager;
import com.zhongyixin.resale.biz.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 支付服务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Slf4j
@Service
public class PaymentServiceImpl implements PaymentService {

    @Resource
    OrderService orderService;

    @Resource
    ServiceOrderService serviceOrderService;


    @Resource
    PayOrderLianlianService payOrderLianlianService;

    @Resource
    IntefaceInfoService intefaceInfoService;


    @Resource
    LianlianPayApiManager lianlianPayApiManager;

    @Resource
    ApplicationContext applicationContext;

    @Resource
    SunshineOrderManager sunshineOrderManager;

    @Resource
    PayRecordService payRecordService;

    @Autowired
    VasOrderPublishRecordService vasOrderPublishRecordService;

    @Resource
    LockManager lockManager;


    @Async("asyncServiceExecutor")
    @Override
    public void sendNewLianlianPaymentRequest(Order order, String payOrderId, ServiceOrder serviceOrder) {
        PayOrderLianlian payOrderLianlian = payOrderLianlianService.selectPayOrderLianlianByPayOrderId(payOrderId);
        if (payOrderLianlian.getStatus() != Integer.parseInt(OrderConstant.INIT_ORDER_STATUS)) {
            throw new RuntimeException("order may be paid. payOrderId: " + payOrderId);
        }

        //锁等待3s
        int lockWaitTime = 30;
        //锁最大释放时间60s
        int lockLeaseTime = 60;

        String lockKey = "lock:lianlian:" + payOrderId;
        DLock lock = lockManager.getLock(lockKey);
        //TODO 请检查锁
        lock.tryLock(lockWaitTime, TimeUnit.SECONDS, () -> {
            PayOrderLianlian payOrderLianLianInDB = payOrderLianlianService.selectPayOrderLianlianByPayOrderId(payOrderId);
            if (payOrderLianLianInDB.getStatus() != Integer.parseInt(OrderConstant.INIT_ORDER_STATUS)) {
                throw new RuntimeException("order may be paid. payOrderId: " + payOrderId);
            }

            //修改为处理中
            payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.DEALING_ORDER_STATUS));
            payOrderLianlianService.updateById(payOrderLianlian);
            //支付记录
            payRecordService.insertPayRecord(order, payOrderLianlian.getStatus(),
                    payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY, null);

            return payOrderLianlian;
        }, () -> {
            throw new RuntimeException("wait timeout, lock key:  " + lockKey);
        });

        //发送连连代付请求
        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setAmount(order.getAmount().toPlainString());
        paymentDTO.setBankCardNo(order.getBankCardNo());
        paymentDTO.setCardName(order.getUserName());
        paymentDTO.setOrderId(payOrderId);
        paymentDTO.setPlatform(order.getApiSource());
        paymentDTO.setInfoOrder(LianlianPayConstant.QUNQIU_INFO_ORDER);
        try {
            lianlianPayApiManager.payment(paymentDTO);
        } catch (Exception e) {
            log.error("lianlian payment 接口异常" , e);
        }
    }

    @Override
    public void paymentSuccessDealLogic(Order order, LocalDateTime dateTime) {
        String apiSource = order.getApiSource();
        Date date = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
        //TODO 怎么调整
//        if (yuJiaApiManager.getPingAnApiManager().getPingAnApiSource().equals(apiSource)) {
//            PingAnPaySuccessWriteOffInput pingAnPaySuccessWriteOffInput = new PingAnPaySuccessWriteOffInput();
//            pingAnPaySuccessWriteOffInput.setCard_code(order.getCouponNo());
//            pingAnPaySuccessWriteOffInput.setSource(apiSource);
//            pingAnPaySuccessWriteOffInput.setApiSource(apiSource);
//            pingAnPaySuccessWriteOffInput.setService_status(YuJiaConstant.SUCCESS_SERVICE_STATUS);
//            pingAnPaySuccessWriteOffInput.setService_exchange_time(DateUtil.formatDateTime(date));
//
//            PingAnPaySuccessWriteOffOutput output = null;
//            boolean flag = false;
//            try {
//                output = yuJiaApiManager.getPingAnApiManager().sendPingAnPaySuccessWriteOffNotice(pingAnPaySuccessWriteOffInput);
//            } catch (Exception e) {
//                log.error("apiSource：{},WriteOffNotice Exception ", apiSource, e);
//                flag = true;
//            }
//            if (flag || output == null || !output.isSuccess()) {
//                YujiaNotifyFailRecord yujiaNotifyFailRecord = new YujiaNotifyFailRecord();
//                yujiaNotifyFailRecord.setType("核销接口");
//                yujiaNotifyFailRecord.setApiSource(apiSource);
//                yujiaNotifyFailRecord.setOrderId(order.getOrderId());
//                yujiaNotifyFailRecord.setParams(JSONObject.getInstance().toJSONString(pingAnPaySuccessWriteOffInput));
//                yujiaNotifyFailRecordService.add(yujiaNotifyFailRecord);
//            }
//
//
//        } else if (yuJiaApiManager.getGuoRenApiManager().getGuoRenApiSource().equals(apiSource)) {
//            try {
//                GuorenBankcardRecord guorenBankcardRecord = new GuorenBankcardRecord();
//                guorenBankcardRecord.setBankCardNo(order.getBankCardNo());
//                guorenBankcardRecord.setTelephone(order.getTelphone());
//                guorenBankcardRecordService.insertOrUpdateGuorenBankcardRecord(guorenBankcardRecord);
//            } catch (Exception exception) {
//                log.error("guoren bankCardNo insert or update fail ,The telephone is {},The bankCardNo is {}", order.getTelphone(), order.getBankCardNo());
//            }
//
//            GuoRenPaySuccessWriteOffInput guoRenPaySuccessWriteOffInput = GuoRenDtoToVoConvert.INSTANCE.orderToOGuoRenPaySuccessWriteOffInput(order);
//            GuoRenPaySuccessWriteOffOutput output = null;
//            boolean flag = false;
//            try {
//                output = yuJiaApiManager.getGuoRenApiManager().sendGuoRenPaySuccessWriteOffNotice(guoRenPaySuccessWriteOffInput);
//            } catch (Exception e) {
//                log.error("apiSource：{},WriteOffNotice Exception", apiSource, e);
//                flag = true;
//            }
//            if (flag || output == null || !output.isSuccess()) {
//                YujiaNotifyFailRecord yujiaNotifyFailRecord = new YujiaNotifyFailRecord();
//                yujiaNotifyFailRecord.setType("核销接口");
//                yujiaNotifyFailRecord.setOrderId(order.getOrderId());
//                yujiaNotifyFailRecord.setApiSource(apiSource);
//                yujiaNotifyFailRecord.setParams(JSONObject.getInstance().toJSONString(guoRenPaySuccessWriteOffInput));
//                yujiaNotifyFailRecordService.add(yujiaNotifyFailRecord);
//            }
//
//        } else if (yuJiaApiManager.getChannelApiManager().getChannelApiSource().equals(apiSource)) {
//            ChannelPaySuccessWriteOffInput channelPaySuccessWriteOffInput = new ChannelPaySuccessWriteOffInput();
//            channelPaySuccessWriteOffInput.setBatchcode(order.getCouponNo());
//            channelPaySuccessWriteOffInput.setService_status(YuJiaConstant.SUCCESS_SERVICE_STATUS);
//            channelPaySuccessWriteOffInput.setService_exchange_time(DateUtil.formatDateTime(date));
//            channelPaySuccessWriteOffInput.setPhone(order.getTelphone());
//            channelPaySuccessWriteOffInput.setApiSource(apiSource);
//            ChannelPaySuccessWriteOffOutput output = null;
//            boolean flag = false;
//            try {
//                output = yuJiaApiManager.getChannelApiManager().sendChannelPaySuccessWriteOffNotice(channelPaySuccessWriteOffInput);
//            } catch (Exception e) {
//                log.error("apiSource：{},WriteOffNotice Exception", apiSource, e);
//                flag = true;
//            }
//            if (flag || output == null || !output.isSuccess()) {
//                YujiaNotifyFailRecord yujiaNotifyFailRecord = new YujiaNotifyFailRecord();
//                yujiaNotifyFailRecord.setType("核销接口");
//                yujiaNotifyFailRecord.setApiSource(apiSource);
//                yujiaNotifyFailRecord.setOrderId(order.getOrderId());
//                yujiaNotifyFailRecord.setParams(JSONObject.getInstance().toJSONString(channelPaySuccessWriteOffInput));
//                yujiaNotifyFailRecordService.add(yujiaNotifyFailRecord);
//            }
//        } else if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(apiSource)) {
//            String orderType = order.getOrderType();
//            if (OrderConstant.OrderType.NEW_YGBH.equals(orderType)) {
//                // 通知crm 海汇
//                publishOrderXConsumeSuccessEvent(order, order.getCardOrderNo());
//            } else {
//                SunshinePaySuccessWriteOffInput sunshinePaySuccessWriteOffInput = new SunshinePaySuccessWriteOffInput();
//                sunshinePaySuccessWriteOffInput.setCard_code(order.getCouponNo());
//                sunshinePaySuccessWriteOffInput.setApiSource(order.getApiSource());
//                sunshinePaySuccessWriteOffInput.setService_exchange_time(DateUtil.formatDateTime(date));
//                sunshinePaySuccessWriteOffInput.setService_status(YuJiaConstant.SUCCESS_SERVICE_STATUS);
//                sunshinePaySuccessWriteOffInput.setSource(order.getApiSource());
//                sunshinePaySuccessWriteOffInput.setSignType("MD5");
//
//                boolean flag = false;
//                SunshinePaySuccessWriteOffOutput output = null;
//                try {
//                    output = yuJiaApiManager.getSunshineManager().sendSunshinePaySuccessWriteOffNotice(sunshinePaySuccessWriteOffInput);
//                } catch (Exception e) {
//                    log.error("apiSource：{},WriteOffNotice Exception", apiSource, e);
//                    flag = true;
//                }
//                if (flag || !output.isSuccess()) {
//                    YujiaNotifyFailRecord yujiaNotifyFailRecord = new YujiaNotifyFailRecord();
//                    yujiaNotifyFailRecord.setType("核销接口");
//                    yujiaNotifyFailRecord.setApiSource(apiSource);
//                    yujiaNotifyFailRecord.setOrderId(order.getOrderId());
//                    yujiaNotifyFailRecord.setParams(JSONObject.getInstance().toJSONString(sunshinePaySuccessWriteOffInput));
//                    yujiaNotifyFailRecordService.add(yujiaNotifyFailRecord);
//                }
//
//                YujiaNotifyRecord yujiaNotifyRecord = new YujiaNotifyRecord();
//                yujiaNotifyRecord.setOrderId(order.getOrderId());
//                yujiaNotifyRecord.setTranDate(LocalDateTime.now());
//                yujiaNotifyRecordService.save(yujiaNotifyRecord);
//
//                SunbeamPaySuccessWriteOffInput sunbeamPaySuccessWriteOffInput = SunshineDtoToVoConvert.INSTANCE.sunshinePaySuccessWriteOffInputToSunbeamPaySuccessWriteOffInput(sunshinePaySuccessWriteOffInput);
//                sunbeamApiManager.sendSunshinePaySuccessWriteOffNotice(sunbeamPaySuccessWriteOffInput);
//            }
//
//        }
    }


    /**
     * 连连代付查询
     *
     * @param payOrderLianlian
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void orderQueryLianlianPayApi(PayOrderLianlian payOrderLianlian) {
        QueryPaymentOut paymentOut;
        try {
            paymentOut = lianlianPayApiManager.queryPayment(payOrderLianlian.getPayOrderId());
        } catch (Exception e) {
            log.error("连连支付查询异常，支付订单号：{}", payOrderLianlian.getPayOrderId(), e);
            return;
        }

        if (paymentOut == null) {
            // 可抛异常，查看原因
            log.error("连连支付查询接口响应异常，支付订单号：{}", payOrderLianlian.getPayOrderId());
            return;
        }

        // 先对结果验签
        boolean signCheck = lianlianPayApiManager.verifySign(JSONObject.getInstance().toJSONString(paymentOut), paymentOut.getSign());
        if (!signCheck) {
            // 传送数据被篡改，可抛出异常，再人为介入检查原因
            log.error("连连支付查询接口响应结果验签异常，支付订单号：{}", payOrderLianlian.getPayOrderId());
            return;
        }

        if (RetCodeEnum.SUCC.getCode().equals(paymentOut.getRet_code())) {
            PaymentStatusEnum paymentStatusEnum = PaymentStatusEnum
                    .getPaymentStatusEnumByValue(paymentOut.getResult_pay());
            // 根据订单状态处理自已的业务逻辑
            switch (Objects.requireNonNull(paymentStatusEnum)) {
                case PAYMENT_APPLY: {
                    // 付款申请，这种情况一般不会发生，如出现，请直接找连连技术处理
                    log.error("连连支付查询状态异常，枚举状态：{}，需连连技术处理，支付订单号：{}", PaymentStatusEnum.PAYMENT_APPLY, payOrderLianlian.getPayOrderId());
                    break;
                }
                case PAYMENT_SUCCESS: {
                    // 成功
                    LocalDateTime tranDate = LocalDateTime.now();
                    //修改订单为成功
                    Order order = new Order();
                    order.setTranDate(tranDate);
                    order.setPaymentNo(payOrderLianlian.getPayOrderId());
                    order.setStatus(Integer.parseInt(OrderConstant.SUCCESS_ORDER_STATUS));
                    order.setOrderId(payOrderLianlian.getOrderId());
                    Integer integer = orderService.updateOrderByOrderId(order);
                    if (integer < 1) {
                        return;
                    }


                    Order orderByOrderId = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());
                    //TODO 这个判断怎么写
//                    if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                    if (true) {
                        //修改阳光订单状态
                        ServiceOrder serviceOrderInDB = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());

                        ServiceOrder serviceOrder = new ServiceOrder();
                        serviceOrder.setOrderId(orderByOrderId.getCardOrderNo());
                        serviceOrder.setPaymentNo(paymentOut.getNo_order());
                        serviceOrder.setPayCompany(orderByOrderId.getPayCompany());
                        serviceOrder.setTranDate(tranDate);
                        // 获取起息日
                        LocalDate startRateDate = tranDate.toLocalDate();
                        if(StrUtil.isNotBlank(serviceOrderInDB.getAgentCode())){
                            // 代理订单 起息日为T+2月的1号
                            startRateDate = startRateDate.plusMonths(2).with(TemporalAdjusters.firstDayOfMonth());
                        }
                        serviceOrder.setStartRateDate(startRateDate);
                        serviceOrder.setStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED));
                        serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_USED));
                        serviceOrder.setSunshineServiceCharge(ServiceOrderConstant.Charge.SERVICECHARGE);
                        serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
                    }


                    //修改连连订单
                    payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.SUCCESS_ORDER_STATUS));
                    String settleDate = paymentOut.getSettle_date();
                    payOrderLianlian.setSettleDate(settleDate);
                    payOrderLianlian.setPaymentOrderNo(paymentOut.getOid_paybill());
                    payOrderLianlian.setInfoOrder(paymentOut.getInfo_order());
                    payOrderLianlian.setMemo(LianlianPayConstant.MEMO);
                    payOrderLianlianService.updatePayOrderLianlianByPayOrderId(payOrderLianlian);

                    //修改记录
                    VasOrderPublishRecord vasOrderPublishRecord = vasOrderPublishRecordService.getByChildOrderNo(String.valueOf(payOrderLianlian.getId()));
                    if (vasOrderPublishRecord != null) {
                        vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.SUCCESS);
                        vasOrderPublishRecord.setPushSuccessTime(LocalDateTime.now());
                        vasOrderPublishRecordService.updateById(vasOrderPublishRecord);
                    }


                    payRecordService.insertPayRecord(orderByOrderId, payOrderLianlian.getStatus(),
                            payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY, "交易成功");

                    //修改响应信息
                    IntefaceInfo intefaceInfo = new IntefaceInfo();
                    intefaceInfo.setRequestId(paymentOut.getNo_order());
                    intefaceInfo.setResponseMsg(JSONObject.getInstance().toJSONString(paymentOut));
                    intefaceInfoService.updateIntefaceInfoByRequestId(intefaceInfo);

                    paymentSuccessDealLogic(orderByOrderId, tranDate);

                    //TODO 怎么调整
//                    if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                    if (true) {
                        //扣减额度
                        ServiceOrder serviceOrderByOrderId = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());
                        sunshineOrderManager.organizeDeductionAmountLogic(orderByOrderId, serviceOrderByOrderId, payOrderLianlian.getPayOrderId());

                        // 国金核销通知
                        try {
                            //TODO 请确认是否需要
//                            guojinBusinessService.sunshineOrderVerificationForGuojin(serviceOrderByOrderId, tranDate);
                        } catch (Exception e) {
                            log.info("lianlianpay querypayment 国金核销阳光订单失败", e);
                        }

                        // 发布事件通知
                        try {
                            applicationContext.publishEvent(new QuotaEvent(serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo()).getOrgParentCode()));
                        } catch (Exception e) {
                            log.info("lianlianpay querypayment 发布事件失败", e);
                        }

                    }

                    break;
                }
                case PAYMENT_CHECK:
                    // PAYMENT_CHECK 复核状态
                    // 返回4002，4003，4004时，订单会处于复核状态，这时还未创建连连支付单，没提交到银行处理，如需对该订单继续处理，需商户先人工审核这笔订单是否是正常的付款请求，没问题后再调用确认付款接口
                    // 如果对于复核状态的订单不做处理，可当做失败订单
                case PAYMENT_FAILURE:
                    // PAYMENT_FAILURE 失败
                case PAYMENT_RETURN:
                    // PAYMENT_RETURN 退款
                    // 可当做失败（退款情况极小概率下会发生，个别银行处理机制是先扣款后打款给用户时再检验卡号信息是否正常，异常时会退款到连连商户账上）
                case PAYMENT_CLOSED:
                    // PAYMENT_CLOSED 关闭 可当做失败 ，对于复核状态的订单不做处理会将订单关闭
                {
                    //失败
                    //修改订单为失败
                    Order order = new Order();
                    order.setTranDate(LocalDateTime.now());
                    order.setPaymentNo(payOrderLianlian.getPayOrderId());
                    order.setFailExplain(paymentOut.getInfo_order());
                    order.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                    order.setOrderId(payOrderLianlian.getOrderId());
                    Integer integer = orderService.updateOrderByOrderId(order);
                    if (integer < 1) {
                        return;
                    }

                    Order orderByOrderId = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());
                    //TODO 怎么调整
//                    if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                    if (true) {
                        //修改阳光订单状态
                        ServiceOrder serviceOrder = new ServiceOrder();
                        serviceOrder.setOrderId(payOrderLianlian.getCardOrderNo());
                        serviceOrder.setStatus(NumberUtil.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                        serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED));
                        serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
                    }

                    //修改连连订单
                    payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                    payOrderLianlian.setPaymentOrderNo(paymentOut.getOid_paybill());
                    payOrderLianlian.setInfoOrder(paymentOut.getInfo_order());
                    payOrderLianlian.setMemo(LianlianPayConstant.MEMO);
                    payOrderLianlianService.updatePayOrderLianlianByPayOrderId(payOrderLianlian);

                    //修改记录
                    VasOrderPublishRecord vasOrderPublishRecord = vasOrderPublishRecordService.getByChildOrderNo(String.valueOf(payOrderLianlian.getId()));
                    if (vasOrderPublishRecord != null) {
                        vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.FAIL);
                        vasOrderPublishRecord.setRemark(paymentOut.getInfo_order());
                        vasOrderPublishRecordService.updateById(vasOrderPublishRecord);
                    }

                    String failMsg;
                    if (PaymentStatusEnum.PAYMENT_CHECK.getValue().equals(paymentOut.getResult_pay())) {
                        failMsg = RetCodeEnum.ORDER_SUSPECT.getMsg();
                    } else {
                        failMsg = paymentOut.getInfo_order().replace(LianlianPayConstant.QUNQIU_INFO_ORDER, "");
                        if (StringUtils.isBlank(failMsg)) {
                            failMsg = RetCodeEnum.ORDER_SUSPECT.getMsg();
                        }
                    }

                    failMsg = failMsg.replace(CommonConstant.UNDERLINE, "");
                    payRecordService.insertPayRecord(orderByOrderId, payOrderLianlian.getStatus(),
                            payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY, failMsg);

                    //TODO 怎么调整
//                    if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                    if (true) {
                        //恢复额度
                        ServiceOrder serviceOrderByOrderId = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());
                        sunshineOrderManager.organizeRecoveryQuotaLogic(orderByOrderId, serviceOrderByOrderId, payOrderLianlian.getPayOrderId());
                    }

                    //修改响应信息
                    IntefaceInfo intefaceInfo = new IntefaceInfo();
                    intefaceInfo.setRequestId(paymentOut.getNo_order());
                    intefaceInfo.setResponseMsg(JSONObject.getInstance().toJSONString(paymentOut));
                    intefaceInfoService.updateIntefaceInfoByRequestId(intefaceInfo);
                    break;
                }
                case PAYMENT_DEALING:
                    // 处理中
                    break;
                default:
                    break;
            }
        } else if (RetCodeEnum.NO_RECORD.getCode().equals(paymentOut.getRet_code())) {
            // 订单不存在，这种情况可以用原单号付款，最好不要换单号，如换单号，在连连商户站确认下改订单是否存在，避免系统并发时返回8901，实际有一笔订单
            Order order = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());
            if (order != null) {
                //发送连连代付请求
                PaymentDTO paymentDTO = new PaymentDTO();
                paymentDTO.setAmount(order.getAmount().toPlainString());
                paymentDTO.setBankCardNo(order.getBankCardNo());
                paymentDTO.setCardName(order.getUserName());
                paymentDTO.setOrderId(payOrderLianlian.getPayOrderId());
                paymentDTO.setPlatform(order.getApiSource());
                paymentDTO.setInfoOrder(LianlianPayConstant.QUNQIU_INFO_ORDER);
                try {
                    PaymentOut payment = lianlianPayApiManager.payment(paymentDTO);

                    // 对返回0000时验证签名
                    if (payment.successed()) {
                        // 已生成连连支付单，付款处理中（交易成功，不是指付款成功，是指跟连连流程正常），商户可以在这里处理自已的业务逻辑（或者不处理，在异步回调里处理逻辑）,最终的付款状态由异步通知回调告知
                    } else if (RetCodeEnum.ORDER_SUSPECT.getCode().equals(payment.getRet_code())
                            || RetCodeEnum.BOTH_SUSPECT.getCode().equals(payment.getRet_code())) {
                    } else if (RetCodeEnum.isNeedQuery(payment.getRet_code())) {
                        // 出现1002，2005，4006，4007，4009，9999这6个返回码时（或者对除了0000之后的code都查询一遍查询接口）调用付款结果查询接口，明确订单状态，不能私自设置订单为失败状态，以免造成这笔订单在连连付款成功了，而商户设置为失败
                        // 第一次测试对接时，返回{"ret_code":"4007","ret_msg":"敏感信息解密异常"},可能原因报文加密用的公钥改动了,demo中的公钥是连连公钥，商户生成的公钥用于上传连连商户站用于连连验签，生成的私钥用于加签
                    } else {
                        // 返回其他code时，可将订单置为失败
                        //失败
                        //修改订单为失败
                        order = new Order();
                        order.setTranDate(LocalDateTime.now());
                        order.setPaymentNo(payOrderLianlian.getPayOrderId());
                        order.setFailExplain(paymentOut.getInfo_order());
                        order.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                        order.setOrderId(payOrderLianlian.getOrderId());
                        Integer integer = orderService.updateOrderByOrderId(order);
                        if (integer < 1) {
                            return;
                        }
                        Order orderByOrderId = orderService.selectOrderByOrderId(payOrderLianlian.getOrderId());
                        //TODO 怎么调整
//                        if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                        if (true) {
                            //修改阳光订单状态
                            ServiceOrder serviceOrder = new ServiceOrder();
                            serviceOrder.setOrderId(payOrderLianlian.getCardOrderNo());
                            serviceOrder.setStatus(NumberUtil.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                            serviceOrder.setRepayStatus(NumberUtil.parseInt(CommonConstant.ServiceOrder.REPAYSTATUS_UNUSED));
                            serviceOrderService.updateServiceOrderRepayStatus(serviceOrder);
                        }

                        //修改连连订单
                        payOrderLianlian.setStatus(Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS));
                        payOrderLianlian.setPaymentOrderNo(payment.getOid_paybill());
                        payOrderLianlian.setMemo(LianlianPayConstant.MEMO);
                        payOrderLianlianService.updatePayOrderLianlianByPayOrderId(payOrderLianlian);


                        payRecordService.insertPayRecord(orderByOrderId, payOrderLianlian.getStatus(),
                                payOrderLianlian.getPayOrderId(), PaymentAisleConstant.LianlianPay.PAY_COMPANY, payment.getRet_msg());

                        //修改记录
                        VasOrderPublishRecord vasOrderPublishRecord = vasOrderPublishRecordService.getByChildOrderNo(String.valueOf(payOrderLianlian.getId()));
                        if (vasOrderPublishRecord != null) {
                            vasOrderPublishRecord.setStatus(VasOrderPublishRecordConstant.Status.FAIL);
                            vasOrderPublishRecord.setRemark(paymentOut.getInfo_order());
                            vasOrderPublishRecordService.updateById(vasOrderPublishRecord);
                        }

                        //TODO 怎么调整
//                        if (yuJiaApiManager.getSunshineManager().getSunshineApiSource().equals(orderByOrderId.getApiSource())) {
                        if (true) {
                            //恢复额度
                            ServiceOrder serviceOrderByOrderId = serviceOrderService.selectServiceOrderByOrderId(orderByOrderId.getCardOrderNo());
                            sunshineOrderManager.organizeRecoveryQuotaLogic(orderByOrderId, serviceOrderByOrderId, payOrderLianlian.getPayOrderId());
                        }

                        //修改响应信息
                        IntefaceInfo intefaceInfo = new IntefaceInfo();
                        intefaceInfo.setRequestId(payment.getNo_order());
                        intefaceInfo.setResponseMsg(JSONObject.getInstance().toJSONString(payment));
                        intefaceInfoService.updateIntefaceInfoByRequestId(intefaceInfo);
                    }
                } catch (Exception e) {
                    log.error("lianlian payment 接口异常" , e);
                    throw new RuntimeException("lianlian payment 接口异常");
                }
            }
        } else {
            // 查询异常（极端情况下才发生,对于这种情况，可人工介入查询，在连连商户站查询或者联系连连客服，查询订单状态）
            log.error("连连支付查询异常，需人工介入查询，支付订单号：{}", payOrderLianlian.getPayOrderId());
        }
    }

    @Override
    public void sinosigOrganizeQuota(Order order, ServiceOrder serviceOrder, String payOrderId, String payWay, String payCompany) {
        try {
            sunshineOrderManager.organizeQuotaLogic(order, serviceOrder, payOrderId);
        } catch (OrganizeExceedTimeOrderException e) {
            sunshineOrderManager.changeSinosigStatusToException(order, serviceOrder, payOrderId, "机构存在账期外未还订单", payCompany, true);
            log.error("机构存在账期外未还订单,orderId is {}", order.getOrderId());
            throw new BusinessException("当前领取渠道正在维护中，请稍后再试");
        } catch (OrganizeNotSufficientFundsException e) {
            sunshineOrderManager.changeSinosigStatusToException(order, serviceOrder, payOrderId, "机构余额不足", payCompany, true);
            log.error("机构余额不足,orderId is {}", order.getOrderId(),e);
            throw new BusinessException("当前领取渠道正在维护中，请稍后再试");
        } catch (OrganizeNoExistException e) {
            sunshineOrderManager.changeSinosigStatusToException(order, serviceOrder, payOrderId, "机构不存在或者已被禁用", payCompany, false);
            log.error("机构不存在或者已被禁用,orderId is {}", order.getOrderId(),e);
            throw new BusinessException("当前领取渠道正在维护中，请稍后再试");
        }
    }


}
