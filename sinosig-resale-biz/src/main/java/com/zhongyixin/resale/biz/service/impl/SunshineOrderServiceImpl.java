package com.zhongyixin.resale.biz.service.impl;

import com.wftk.jackson.core.JSONObject;
import com.zhongyixin.resale.biz.common.constant.OpenApiConstant;
import com.zhongyixin.resale.biz.common.constant.OrderConstant;
import com.zhongyixin.resale.biz.common.enums.ErrorCodeEnum;
import com.zhongyixin.resale.biz.common.exception.SinosigApiBadBizException;
import com.zhongyixin.resale.biz.common.input.CancelOrderDTO;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.entity.Order;
import com.zhongyixin.resale.biz.entity.ServiceOrder;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyDTO;
import com.zhongyixin.resale.biz.manage.SunshineOrderManager;
import com.zhongyixin.resale.biz.service.OrderService;
import com.zhongyixin.resale.biz.service.ServiceOrderService;
import com.zhongyixin.resale.biz.service.SunshineOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @@create 2024/3/6 13:44
 */
@Service
@Slf4j
public class SunshineOrderServiceImpl implements SunshineOrderService {

    @Autowired
    private OrderService orderService;

    @Resource
    ServiceOrderService serviceOrderService;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    SunshineOrderManager sunshineOrderManager;

    @Override
    @Transactional
    public void updateOrder(UpdateOrderDTO dto) {
        if(StringUtils.isBlank(dto.getOrderId())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.ORDER_ID_BLANK.getCode(),ErrorCodeEnum.ORDER_ID_BLANK.getMessage());
        }else if(StringUtils.isBlank(dto.getModifyToName())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.MODIFY_TO_NAME_BLANK.getCode(),ErrorCodeEnum.MODIFY_TO_NAME_BLANK.getMessage());
        }else if(StringUtils.isBlank(dto.getModifyToIdNo())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.MODIFY_TO_ID_NO_BLANK.getCode(),ErrorCodeEnum.MODIFY_TO_ID_NO_BLANK.getMessage());
        }


        Order order = verifyTheExistenceOfTheOrder(dto.getOrderId());
        if (order.getStatus() != Integer.parseInt(OrderConstant.INIT_ORDER_STATUS)
                && order.getStatus() != Integer.parseInt(OrderConstant.FAIL_ORDER_STATUS)) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_UPDATE.getCode(), ErrorCodeEnum.NOT_UPDATE.getMessage());
        }

        // 写锁
        RLock rLock = getOrderReadWriteLock(dto.getOrderId()).writeLock();

        try {
            if (!rLock.tryLock(10, 20 * 1000 * 60, TimeUnit.MILLISECONDS)) {
                throw new SinosigApiBadBizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), ErrorCodeEnum.SYSTEM_ERROR.getMessage());
            }else {
                sunshineOrderManager.updateOrder(order,dto);
            }
        } catch (InterruptedException interrupted) {
            throw new SinosigApiBadBizException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), ErrorCodeEnum.SYSTEM_ERROR.getMessage());
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                log.info("unlocked. thread: {}", Thread.currentThread().getId());
                rLock.unlock();
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(CancelOrderDTO dto) {
        log.info("sinosig cancel order params:{}", JSONObject.getInstance().toJSONString(dto));
        if(StringUtils.isBlank(dto.getOrderId())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.ORDER_ID_BLANK.getCode(),ErrorCodeEnum.ORDER_ID_BLANK.getMessage());
        }
        Order order = verifyTheExistenceOfTheOrder(dto.getOrderId());
        if(order.getStatus() != 0){
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_CANCEL.getCode(),ErrorCodeEnum.NOT_CANCEL.getMessage());
        }
        if(!orderService.cancelOrder(dto.getOrderId())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_CANCEL.getCode(),ErrorCodeEnum.NOT_CANCEL.getMessage());
        }
        if(serviceOrderService.cancelOrder(dto.getOrderId())){
            throw new SinosigApiBadBizException(ErrorCodeEnum.NOT_CANCEL.getCode(),ErrorCodeEnum.NOT_CANCEL.getMessage());
        }

    }



    private Order verifyTheExistenceOfTheOrder(String orderId){
        Order order = orderService.selectOrderByCardOrderNo(orderId);
        if(order == null){
            throw new SinosigApiBadBizException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(), ErrorCodeEnum.ORDER_NOT_EXIST.getMessage());
        }
        ServiceOrder serviceOrder = serviceOrderService.selectServiceOrderByOrderId(orderId);
        if(serviceOrder == null){
            throw new SinosigApiBadBizException(ErrorCodeEnum.ORDER_NOT_EXIST.getCode(),ErrorCodeEnum.ORDER_NOT_EXIST.getMessage());
        }
        return order;
    }


    @Override
    public void cashNotify(SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO) {

    }

    private RReadWriteLock getOrderReadWriteLock(String orderId) {
        String lockKey = OpenApiConstant.LockKey.ORDER_LOCK + orderId;
        return redissonClient.getReadWriteLock(lockKey);
    }
}
