package com.zhongyixin.resale.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wftk.jackson.core.JSONObject;
import com.wftk.lock.spring.boot.autoconfigure.core.dlock.DLock;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.constant.OrderProxyConstant;
import com.zhongyixin.resale.biz.common.event.OrderProxyPayFailEvent;
import com.zhongyixin.resale.biz.common.event.OrderProxyPaySuccessEvent;
import com.zhongyixin.resale.biz.common.input.LianlianPayParamsDTO;
import com.zhongyixin.resale.biz.common.output.LianLianPayCallbackRespOutput;
import com.zhongyixin.resale.biz.entity.OpenChannelInfo;
import com.zhongyixin.resale.biz.entity.OrderProxy;
import com.zhongyixin.resale.biz.entity.OrderProxyExceptionMsg;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.PaymentStatusEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.enums.RetCodeEnum;
import com.zhongyixin.resale.biz.ext.lianlianpay.input.PaymentNoticeInput;
import com.zhongyixin.resale.biz.ext.lianlianpay.security.LLianPayYtSignature;
import com.zhongyixin.resale.biz.ext.lianlianpay.security.RSASign;
import com.zhongyixin.resale.biz.mapper.OrderProxyExceptionMsgMapper;
import com.zhongyixin.resale.biz.mapper.OrderProxyMapper;
import com.zhongyixin.resale.biz.service.LianLianPayCallbackRecordService;
import com.zhongyixin.resale.biz.service.LianLianPayService;
import com.zhongyixin.resale.biz.service.OpenChannelInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class LianLianPayServiceImpl implements LianLianPayService {


    @Autowired
    LianLianPayCallbackRecordService lianLianPayCallbackRecordService;

    @Autowired
    LockManager lockManager;

    @Autowired
    OrderProxyMapper orderProxyMapper;

    @Autowired
    OrderProxyExceptionMsgMapper orderProxyExceptionMsgMapper;

    @Autowired
    OpenChannelInfoService openChannelInfoService;

    @Autowired
    ApplicationContext applicationContext;


    @Override
    public LianLianPayCallbackRespOutput disposeCallback(PaymentNoticeInput notifyInput) {
        log.warn("lian lian pay callback pay result is {}",StrUtil.toString(notifyInput));
        if (notifyInput == null) {
            return LianLianPayCallbackRespOutput.builder()
                    .ret_code(RetCodeEnum.SYSTEM_ERROR.code)
                    .ret_msg(RetCodeEnum.SYSTEM_ERROR.msg).build();
        }
        boolean result = verify(JSONObject.getInstance().toJSONString(notifyInput), notifyInput.getSign(), notifyInput.getOid_partner());
        if (!result) {
            log.warn("verify result is false {}",notifyInput.getNo_order());
            return LianLianPayCallbackRespOutput.builder()
                    .ret_code(RetCodeEnum.SIGN_ERROR.code)
                    .ret_msg(RetCodeEnum.SIGN_ERROR.msg).build();
        }
        // 保存回调记录
        lianLianPayCallbackRecordService.save(notifyInput);
        String resultPay = notifyInput.getResult_pay();
        String orderNo = notifyInput.getNo_order();
        if (StrUtil.isBlank(orderNo)) {
            return LianLianPayCallbackRespOutput.builder()
                    .ret_code(RetCodeEnum.SYSTEM_ERROR.code)
                    .ret_msg("no_order is blank").build();
        }
        //锁等待3s
        int lockWaitTime = 30;
        //锁最大释放时间60s
        int lockLeaseTime = 60;
        DLock lock = lockManager.getLock("disposed_lianlianpay_order_" + orderNo);
        return lock.tryLock(lockWaitTime, TimeUnit.SECONDS,()->{
            OrderProxy orderProxy = orderProxyMapper.getByOrderNo(orderNo);
            if (orderProxy == null) {
                return LianLianPayCallbackRespOutput.builder()
                        .ret_code(RetCodeEnum.SYSTEM_ERROR.code)
                        .ret_msg("原交易请求中传入的商户订单号" + orderNo + "不存在").build();
            }
            if (OrderProxyConstant.STATUS.FAIL.equals(orderProxy.getStatus())
                    || OrderProxyConstant.STATUS.SUCCESS.equals(orderProxy.getStatus())) {
                // 如果订单已经是处理成功或者失败了，那么不再继续处理，防止连连第一次通知成功或失败，第二次通知失败或成功的情况
                OrderProxyExceptionMsg msg = new OrderProxyExceptionMsg();
                msg.setDescriptionMsg("代付订单回调通知状态变更警告，由状态"+orderProxy.getStatus()+"转变"
                        +(PaymentStatusEnum.PAYMENT_SUCCESS.getValue().equals(resultPay)?
                        OrderProxyConstant.STATUS.SUCCESS:OrderProxyConstant.STATUS.FAIL));
                msg.setOrderNo(orderProxy.getOrderNo());
                orderProxyExceptionMsgMapper.insert(msg);
                return LianLianPayCallbackRespOutput.builder()
                        .ret_code(RetCodeEnum.SUCC.code)
                        .ret_msg(RetCodeEnum.SUCC.msg).build();
            }
            if (PaymentStatusEnum.PAYMENT_SUCCESS.getValue().equals(resultPay)) {
                orderProxy.setStatus(OrderProxyConstant.STATUS.SUCCESS);
                orderProxy.setTradeFinishDate(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS));
                orderProxyMapper.updateById(orderProxy);
                applicationContext.publishEvent(new OrderProxyPaySuccessEvent(orderProxy));
            } else if (PaymentStatusEnum.PAYMENT_FAILURE.getValue().equals(resultPay)) {
                orderProxy.setStatus(OrderProxyConstant.STATUS.FAIL);
                orderProxy.setRemark(notifyInput.getInfo_order());
                orderProxy.setTradeFinishDate(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS));
                orderProxyMapper.updateById(orderProxy);
                applicationContext.publishEvent(new OrderProxyPayFailEvent(orderProxy));
            } else if (PaymentStatusEnum.PAYMENT_RETURN.getValue().equals(resultPay)){
                orderProxy.setStatus(OrderProxyConstant.STATUS.FAIL);
                orderProxy.setRemark(notifyInput.getInfo_order());
                orderProxy.setTradeFinishDate(LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS));
                orderProxyMapper.updateById(orderProxy);
                applicationContext.publishEvent(new OrderProxyPayFailEvent(orderProxy));
            }
            return LianLianPayCallbackRespOutput.builder()
                    .ret_code(RetCodeEnum.SUCC.code)
                    .ret_msg(RetCodeEnum.SUCC.msg).build();
        },()->{
            return LianLianPayCallbackRespOutput.builder()
                    .ret_code(RetCodeEnum.SYSTEM_ERROR.code)
                    .ret_msg("dispose order is timeout").build();
        });

    }




    @Override
    public boolean verify(String jsonStr, String sign, String oidPartner) {
        boolean result = false;

        // TODO 连连需要通过商户号查询出配置
        LianlianPayParamsDTO lianlianPayParamsDTO = findChannelInfoByOidPartner(OrderProxyConstant.CHANNEL.LL, oidPartner);
        if(lianlianPayParamsDTO == null){
            return result;
        }
        return RSASign.getInstance().checksign(lianlianPayParamsDTO.getPublicKey(),
                LLianPayYtSignature.getInstance().sign(lianlianPayParamsDTO.getPrivateKey(),JSONObject.getInstance().parseMap(jsonStr,String.class,Object.class)),
                sign);

        // 获取配置
//        OpenChannelInfo openChannelInfo = openChannelInfoService.selectOneByChannelCode(OrderProxyConstant.CHANNEL.LL, 1);
//        if (openChannelInfo == null) {
//            return result;
//        }
//        log.info("lianliian prp is {}", openChannelInfo);
//        if (StrUtil.isBlank(openChannelInfo.getChannelParams())) {
//            log.error("[{}]lianliian pay params is null , open channel info is {}",
//                    "business_warn", openChannelInfo.getChannelCode());
//            return result;
//        }
//
//        LianlianPayParamsDTO lianlianPayParamsDTO;
//        try {
//            lianlianPayParamsDTO = JSONObject.getInstance().parseObject(openChannelInfo.getChannelParams(), LianlianPayParamsDTO.class);
//        } catch (Exception e) {
//            log.error("[{}]lianliian pay params cast error , open channel info is {} , params is {}",
//                    "business_warn", openChannelInfo.getChannelCode(), openChannelInfo.getChannelParams());
//            return result;
//        }
//        return TraderRSAUtil.checksign(lianlianPayParamsDTO.getPublicKey(),
//                SignUtil.genSignData(com.alibaba.fastjson.JSONObject.parseObject(jsonStr)),
//                sign);
    }

    public LianlianPayParamsDTO findChannelInfoByOidPartner(String code, String oidPartner) {
        List<OpenChannelInfo> openChannelInfos = openChannelInfoService.fuzzyFindByChannelCode(OrderProxyConstant.CHANNEL.LL, 1);

        for (OpenChannelInfo openChannelInfo:openChannelInfos) {
            LianlianPayParamsDTO lianlianPayParamsDTO;
            try {
                lianlianPayParamsDTO = JSONObject.getInstance().parseObject(openChannelInfo.getChannelParams(), LianlianPayParamsDTO.class);
                if(oidPartner.equals(lianlianPayParamsDTO.getOidPartner())){
                    log.info("lianliian prp is {}", openChannelInfo);
                    return lianlianPayParamsDTO;
                }
            } catch (Exception e) {
                log.error("[{}]lianliian pay params cast error , open channel info is {} , params is {}",
                        "business_warn", openChannelInfo.getChannelCode(), openChannelInfo.getChannelParams());
                return null;
            }
        }
        return null;
    }


}
