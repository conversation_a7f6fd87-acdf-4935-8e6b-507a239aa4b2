package com.zhongyixin.resale.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 机构告警短信通知表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_org_sms")
public class OrgSms implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构代码
     */
    private String orgCode;

    /**
     * 告警额度
     */
    private BigDecimal qount;

    /**
     * 接收短信通知的机构手机号
     */
    private String phone;

    /**
     * 短信内容
     */
    private String msg;

    /**
     * 还款短信内容
     */
    private String repayMsg;

    /**
     * 开关
     */
    private Integer onOff;


}
