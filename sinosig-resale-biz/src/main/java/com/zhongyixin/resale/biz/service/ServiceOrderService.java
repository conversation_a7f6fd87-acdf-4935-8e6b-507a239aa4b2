package com.zhongyixin.resale.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhongyixin.resale.biz.common.input.*;
import com.zhongyixin.resale.biz.entity.ServiceOrder;

/**
 * <p>
 * 阳光明细订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface ServiceOrderService extends IService<ServiceOrder> {


    /**
     * 查询订单
     * @param orderId
     * @return
     */
    ServiceOrderCopyDTO selectServiceOrderCopyDTOByOrderId(String orderId);

    /**
     * 查询订单
     * @param orderId
     * @return
     */
    ServiceOrder selectServiceOrderByOrderId(String orderId);

    boolean updateOrder(UpdateOrderDTO dto);


    Integer updateServiceOrderRepayStatus(ServiceOrder serviceOrder);

    boolean cancelOrder(String orderId);

    /**
     * 根据父级机构编码、垫资状态获取超过订单周期的订单数目
     * @param parentOrgCode
     * @return
     */
    int getOrderNumbserByOrg(String parentOrgCode);
}
