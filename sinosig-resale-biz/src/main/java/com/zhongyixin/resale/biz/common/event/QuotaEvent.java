package com.zhongyixin.resale.biz.common.event;

import com.zhongyixin.resale.biz.entity.OrgSms;
import org.springframework.context.ApplicationEvent;


/**
 * 垫资额度事件
 * <AUTHOR>
 * @create 2023/3/22 15:46
 */
public class QuotaEvent extends ApplicationEvent {

    String orgCode;
    private String repayQuota;
    private OrgSms orgSms;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }
    public String getRepayQuota() {
        return repayQuota;
    }

    public void setRepayQuota(String repayQuota) {
        this.repayQuota = repayQuota;
    }

    public OrgSms getOrgSms() {
        return orgSms;
    }

    public void setOrgSms(OrgSms orgSms) {
        this.orgSms = orgSms;
    }
    public QuotaEvent(Object source, String orgCode, String repayQuota, OrgSms orgSms) {
        super(source);
        this.orgCode = orgCode;
        this.repayQuota = repayQuota;
        this.orgSms = orgSms;
    }
    public QuotaEvent(Object orgCode) {
        super(orgCode);
    }
}
