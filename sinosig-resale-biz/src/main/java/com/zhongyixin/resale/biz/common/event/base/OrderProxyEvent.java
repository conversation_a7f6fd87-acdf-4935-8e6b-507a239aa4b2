package com.zhongyixin.resale.biz.common.event.base;

import com.zhongyixin.resale.biz.entity.OrderProxy;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 *
 */
@Getter
@Setter
public abstract class OrderProxyEvent extends ApplicationEvent {

    private OrderProxy orderProxy;

    public OrderProxyEvent(OrderProxy orderProxy) {
        super(orderProxy);
        this.orderProxy = orderProxy;
    }
}
