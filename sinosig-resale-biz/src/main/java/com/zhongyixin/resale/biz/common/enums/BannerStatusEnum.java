package com.zhongyixin.resale.biz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @create 2024/3/6 14:31
 */
@Getter
public enum BannerStatusEnum {


    NOMAL(0, "正常"),
    OFFLINE(1, "下线"),
    ;

    private Integer value;
    private String desc;

    BannerStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }



}
