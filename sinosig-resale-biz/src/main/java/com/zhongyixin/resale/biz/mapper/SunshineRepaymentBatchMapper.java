package com.zhongyixin.resale.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhongyixin.resale.biz.common.output.SunshineRepaymentOrderVO;
import com.zhongyixin.resale.biz.entity.SunshineRepaymentBatch;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 阳光还款批次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
public interface SunshineRepaymentBatchMapper extends BaseMapper<SunshineRepaymentBatch> {

//    /**
//     * 分页查询还款批次
//     * @param sunshineRepaymentBatchSearchDTO
//     * @param page
//     * @return
//     */
//    @DataPermission(dataScopes = {
//            @DataScopeType(dataScope = DataScope.SUNSHINE_DATA, field = "org_code" )
//    })
    //TODO 请确认
//    IPage<SunshineRepaymentBatch> queryPageList(IPage<SunshineRepaymentBatch> page,@Param("sunshineRepaymentBatchSearchDTO") SunshineRepaymentBatchSearchDTO sunshineRepaymentBatchSearchDTO );


    /**
     * 根据状态和批次号修改还款流水信息
     * @param repayBatchNo
     * @param paramStatus
     * @param conditionStatus
     * @param memo
     * @param endRateDay
     * @param platRepaymentNo
     * @return
     */
    int updateFlowRepayStatus(@Param("repayBatchNo") String repayBatchNo,
                              @Param("paramStatus") int paramStatus,
                              @Param("conditionStatus") int conditionStatus,
                              @Param("memo") String memo,
                              @Param("endRateDay") String endRateDay,
                              @Param("platRepaymentNo") String platRepaymentNo);

    /**
     * 根据批次号查询二级机构
     * @param repayBatchNo
     * @return
     */
    String selectOrgCodeByRepayBatchNo(String repayBatchNo);

    /**
     * 根据批次号查询还款批次
     * @param repayBatchNo
     * @return
     */
    SunshineRepaymentBatch selectOneByRepayBatchNo(String repayBatchNo);

    /**
     * 根据业务流水号查询还款批次
     * @param platRepaymentNo
     * @return
     */
    SunshineRepaymentBatch selectOneByPlatRepaymentNo(String platRepaymentNo);

    /**
     * 修改关联状态
     * @param repayBatchNo
     * @param paramAssociationStatus
     * @return
     */
    int updateAssociationStatus(@Param("repayBatchNo") String repayBatchNo,@Param("paramAssociationStatus") int paramAssociationStatus);

    /**
     * 查询待重试的批次
     * @return
     */
    List<SunshineRepaymentBatch> selectRetryRepaymentBatchList();

    /**
     * 查询创建时间超过两个小时关联中的批次
     * @return
     */
    List<SunshineRepaymentBatch> selectAssociationRepaymentBatchList();


    List<SunshineRepaymentOrderVO> selectSunshineRepaymentOrderVOList(String repaymentId);

    int chageBatchEndDate(@Param("repayBatchNo")String repayBatchNo,@Param("endDate")String endDate);

    List<String> selectSunshineOrderId(@Param("repayBatchNo")String repayBatchNo);

    int chageServiceOrderEndDate(@Param("sunshineOrderIds")List<String> sunshineOrderIds,@Param("endDate")String endDate);

}
