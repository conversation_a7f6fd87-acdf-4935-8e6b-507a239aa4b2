package com.zhongyixin.resale.biz.service;


import com.zhongyixin.resale.biz.common.input.CancelOrderDTO;
import com.zhongyixin.resale.biz.common.input.UpdateOrderDTO;
import com.zhongyixin.resale.biz.ext.sinosig.dto.SinosigOrderCashNotifyDTO;

/**
 * <AUTHOR>
 * @create 2024/3/6 13:44
 */
public interface SunshineOrderService {



    /**
     * 修改订单
     * @param dto
     * @return
     */
    void updateOrder(UpdateOrderDTO dto);

    /**
     * 取消订单
     * @param dto
     * @return
     */
    void cancelOrder(CancelOrderDTO dto);


    void  cashNotify(SinosigOrderCashNotifyDTO sinosigOrderCashNotifyDTO);



}
