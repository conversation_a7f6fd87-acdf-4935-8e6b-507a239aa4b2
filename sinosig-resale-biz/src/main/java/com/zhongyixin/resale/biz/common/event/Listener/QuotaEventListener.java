package com.zhongyixin.resale.biz.common.event.Listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhongyixin.resale.biz.common.constant.CommonConstant;
import com.zhongyixin.resale.biz.common.event.OrderCreateSucceeEvent;
import com.zhongyixin.resale.biz.common.event.QuotaEvent;
import com.zhongyixin.resale.biz.common.exception.OrgSmsClosedException;
import com.zhongyixin.resale.biz.entity.OrgSms;
import com.zhongyixin.resale.biz.entity.OrganizeYgry;
import com.zhongyixin.resale.biz.mapper.OrgSmsMapper;
import com.zhongyixin.resale.biz.service.OrganizeYgryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


/**
 * 垫资额度事件
 * <AUTHOR>
 * @create 2023/3/22 15:46
 */
@Slf4j
@Component
public class QuotaEventListener implements ApplicationListener<QuotaEvent> {



    @Autowired
    OrganizeYgryService organizeYgryService;

    @Autowired
    OrgSmsMapper orgSmsMapper;

    @Override
    public void onApplicationEvent(QuotaEvent event) {
        try{
            String orgCode = String.valueOf(event.getSource());

            OrganizeYgry organizeYgry = organizeYgryService.selectOrganize(orgCode);
            if(organizeYgry == null){
                log.warn("没有该机构[{}]",orgCode);
                return;
            }
            BigDecimal balance = organizeYgry.getBalance();

            LambdaQueryWrapper<OrgSms> queryOrgSmsWrapper = new LambdaQueryWrapper<>();
            queryOrgSmsWrapper.eq(OrgSms::getOrgCode, orgCode);
            OrgSms orgSmsObj = orgSmsMapper.selectOne(queryOrgSmsWrapper);
            if(orgSmsObj == null ){
                throw new RuntimeException("orgSms is null. orgCode: " + orgCode);
            }

            if(CommonConstant.Sms.OFF.equals(orgSmsObj.getOnOff())){
                throw new OrgSmsClosedException("orgSms may be closed. orgCode: " + orgCode);
            }

            BigDecimal warnqount = orgSmsObj.getQount();

            if (balance.compareTo(warnqount) < 0) {
                LambdaUpdateWrapper<OrgSms> updateOrgSmsWrapper = new LambdaUpdateWrapper<>();
                updateOrgSmsWrapper.set(OrgSms::getOnOff,CommonConstant.Sms.OFF);
                updateOrgSmsWrapper.eq(OrgSms::getOnOff,CommonConstant.Sms.ON);
                updateOrgSmsWrapper.eq(OrgSms::getId,orgSmsObj.getId());
                int update = orgSmsMapper.update(null, updateOrgSmsWrapper);
                if(update > 0){
                    String msg = String.format(orgSmsObj.getMsg(), orgCode, warnqount, balance);
//                    this.sendSms(orgSmsObj.getPhone(), msg);
                }else{
                    throw new OrgSmsClosedException("orgSms may be closed. orgCode: " + orgCode);
                }
            }

        } catch (OrgSmsClosedException e){
            log.warn(e.getMessage());
        }catch (Exception e){
            log.error("系统异常",e);
        }
    }
}
