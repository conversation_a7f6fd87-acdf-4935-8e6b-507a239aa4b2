package com.zhongyixin.resale.biz.common.exception;


import com.wftk.exception.common.ErrorCode;
import com.wftk.exception.common.GlobalErrorConstants;
import com.wftk.exception.core.exception.BaseBusinessException;

/**
 * <AUTHOR>
 * @date  2023/4/14 18:22
 */
public class OrganizeNotSufficientFundsException extends BaseBusinessException {

    private static final long serialVersionUID = -9040723087491516359L;
    private int code;

    public OrganizeNotSufficientFundsException() {
        this(GlobalErrorConstants.INTERNAL_SERVER_ERROR);
    }

    public OrganizeNotSufficientFundsException(ErrorCode errorCode) {
        this(errorCode.code(), errorCode.message());
    }

    public OrganizeNotSufficientFundsException(int code, String message) {
        super(message);
        this.code = code;
    }

    public OrganizeNotSufficientFundsException(String message) {
        super(message);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public OrganizeNotSufficientFundsException(Throwable throwable) {
        super(throwable);
        this.code = GlobalErrorConstants.INTERNAL_SERVER_ERROR.code();
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
