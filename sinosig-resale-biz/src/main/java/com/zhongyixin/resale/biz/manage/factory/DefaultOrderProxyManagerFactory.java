package com.zhongyixin.resale.biz.manage.factory;

import cn.hutool.core.collection.CollectionUtil;
import com.zhongyixin.resale.biz.manage.pay.OrderProxyManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class DefaultOrderProxyManagerFactory implements OrderProxyManagerFactory {

    private final List<OrderProxyManager> list;

    public DefaultOrderProxyManagerFactory(List<OrderProxyManager> list){
        this.list = list;
    }

    @Override
    public OrderProxyManager get(String channel) {
        if (CollectionUtil.isEmpty(list)) {
            log.info("order manager is empty");
            return null;
        }
        for (OrderProxyManager orderProxyManager : list) {
            if (orderProxyManager.support(channel)) {
                return orderProxyManager;
            }
        }
        log.info("order manager is not adapter");
        return null;
    }
}
