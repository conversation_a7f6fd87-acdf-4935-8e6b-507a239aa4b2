package com.zhongyixin.resale.biz.manage.pay;

import com.zhongyixin.resale.biz.common.constant.OrderProxyConstant;
import com.zhongyixin.resale.biz.entity.OrderProxy;
import com.zhongyixin.resale.biz.service.LianLianPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class LianLianPayOrderProxyManager extends OrderProxyManager {

    @Autowired
    LianLianPayService lianLianPayService;

    @Override
    public void create(OrderProxy orderProxy) {
//        lianLianPayService.createOrder(orderProxy);
    }

    @Override
    public void sendNotify(OrderProxy orderProxy) {
//        lianLianPayService.sendNotify(orderProxy);
    }

    @Override
    public boolean support(String s) {
        return OrderProxyConstant.CHANNEL.LL.equals(s);
    }
}
