package com.zhongyixin.resale.biz.config;

import com.wftk.lock.spring.boot.autoconfigure.core.factory.DLockFactory;
import com.wftk.log.spring.boot.autoconfigure.core.MDCTaskDecorator;
import com.zhongyixin.resale.biz.common.Lock.LockManager;
import com.zhongyixin.resale.biz.common.Lock.RedissonLockManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ApplicationConfiguration {

    @Bean
    LockManager lockManager(DLockFactory<?> dLockFactory) {
        return new RedissonLockManager(dLockFactory);
    }


    @Bean
    public Executor asyncServiceExecutor(MDCTaskDecorator mdcTaskDecorator) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数 30
        executor.setCorePoolSize(30);
        //配置最大线程数
        executor.setMaxPoolSize(50);
        //配置队列大小
        executor.setQueueCapacity(100);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("biz-");

        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        executor.setAllowCoreThreadTimeOut(true);
        executor.setKeepAliveSeconds(300);
        //设置线程池关闭的时候等待所有任务都完成再继续销毁其他的bean
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //设置线程池中任务的等待时间，如果超过这个时候还没有就强制销毁，以确保应用最后能够关闭而不是阻塞住
        executor.setAwaitTerminationSeconds(60);
        //执行初始化
        executor.setTaskDecorator(mdcTaskDecorator);
        executor.initialize();
        return executor;
    }

}
