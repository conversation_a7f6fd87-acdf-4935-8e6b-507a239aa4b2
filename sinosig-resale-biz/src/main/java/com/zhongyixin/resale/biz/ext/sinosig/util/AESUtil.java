package com.zhongyixin.resale.biz.ext.sinosig.util;

import com.sinosig.ec.security.AesEcb;
import com.sinosig.ec.security.HEX;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * @Description: AES加密
 * @Author: panxicheng-ghq
 * @CreateDate: 2018/09/12
 * @CreateBy IntelliJ IDEA
 */
public class AESUtil {

    private static Log logger = LogFactory.getLog(AESUtil.class);
    private static final String KEY_CIPHER_ECB = "AES/ECB/PKCS5Padding";

    /**
     * 加密数据
     *
     * @param value
     * @return
     */
    public static String encrypt(String value, String secret_key) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        value = value.trim();
        if (value.length() % 16 == 0) {
            try {
                String decValue = new String(decrypt(HEX.hexString2Bytes(value), secret_key), "utf-8");
                if (StringUtils.isNotBlank(decValue)) {
                    logger.info("EncAndDecUtil#setDataEnc 原数据可以被解密，返回原值，原值：" + value + "，解密后：" + decValue);
                    return value;
                }
            } catch (Exception e2) {
            }
        }
        try {
            return HEX.bytes2HexString(encrypt(value.getBytes("utf-8"), secret_key));
        } catch (Exception e) {
            logger.info("EncAndDecUtil#setDataEnc 加密发生异常：" + e + "，输入值为：" + value);
            return value;
        }
    }

    /**
     * 解密数据
     *
     * @param encValue
     * @return
     */
    public static String decrypt(String encValue, String secret_key) {
        try {
            if (StringUtils.isBlank(encValue)) {
                return encValue;
            }
            encValue = encValue.trim();
            String decValue = new String(decrypt(HEX.hexString2Bytes(encValue), secret_key), "utf-8");
            return StringUtils.isBlank(decValue) ? encValue : decValue;
        } catch (Exception e) {
            logger.error("EncAndDecUtil#setDataDec 解密发生异常：" + e.toString() + "，输入值为：" + encValue);
            return encValue;
        }
    }

    private static byte[] encrypt(byte[] data, String secret_key) throws Exception {

        if (StringUtils.isBlank(secret_key)) {
            throw new Exception("Please configure the ec.project.db.eccreat.aeskey env param.");
        }
        AesEcb aes = new AesEcb(secret_key.getBytes("utf-8"));
        return aes.encrypt(data);
    }

    private static byte[] decrypt(byte[] content, String secret_key) throws Exception {

        if (StringUtils.isBlank(secret_key)) {
            throw new Exception("Please configure the ec.project.db.eccreat.aeskey env param.");
        }
        AesEcb aes = new AesEcb(secret_key.getBytes("utf-8"));
        return aes.decrypt(content);
    }

    public static String encryptBaotu(String content, String password) {
        try {
            SecretKeySpec key = new SecretKeySpec(password.getBytes(), "AES");// 转换为AES专用密钥
            Cipher cipher = null;// 创建密码器
            cipher = Cipher.getInstance(KEY_CIPHER_ECB);
            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化为解密模式的密码器
            byte[] result = cipher.doFinal(content.getBytes());
            return new String(bytesToHex(result));
        } catch (NoSuchAlgorithmException e) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, e);
        } catch (NoSuchPaddingException e) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, e);
        } catch (InvalidKeyException e) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, e);
        } catch (BadPaddingException e) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, e);
        } catch (IllegalBlockSizeException e) {
            Logger.getLogger(AESUtil.class.getName()).log(Level.SEVERE, null, e);
        }
        return null;
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }
}
