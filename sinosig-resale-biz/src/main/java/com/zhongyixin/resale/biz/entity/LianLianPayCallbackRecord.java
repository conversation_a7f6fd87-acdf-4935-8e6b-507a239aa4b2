package com.zhongyixin.resale.biz.entity;

import com.zhongyixin.resale.biz.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *  连连回调记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LianLianPayCallbackRecord extends BaseEntity {


    private static final long serialVersionUID = -3149131810060137474L;
    /**
     * 原请求中商户订单号
     */
    private String noOrder;

    /**
     * 商户编号是商户在连连支付支付平台上开设的商户号码
     */
    private String oidPartner;

    /**
     * 连连订单创建成功时返回
连连支付单号。 全局唯一
     */
    private String oidPaybill;

    /**
     * RSA
     */
    private String signType;

    /**
     * 签名
     */
    private String sign;

    /**
     * 商户订单时间
     */
    private String dtOrder;

    /**
     * 原请求中交易金额。
     */
    private String moneyOrder;

    /**
     * APPLY， 付款申请。
CHECK， 复核申请。
SUCCESS， 成功。
PROCESSING，付款处理中。
CANCEL，付款退款， 付款成功后，有可能发生退款。
FAILURE， 失败 。
CLOSED， 付款关闭。
     */
    private String resultPay;

    /**
     * 清算日期
     */
    private String settleDate;

    /**
     * 原订单有infor_order或打款失败传入
     */
    private String infoOrder;


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String DELETED = "deleted";

    public static final String UPDATE_TIME = "update_time";

    public static final String NO_ORDER = "no_order";

    public static final String OID_PARTNER = "oid_partner";

    public static final String OID_PAYBILL = "oid_paybill";

    public static final String SIGN_TYPE = "sign_type";

    public static final String SIGN = "sign";

    public static final String DT_ORDER = "dt_order";

    public static final String MONEY_ORDER = "money_order";

    public static final String RESULT_PAY = "result_pay";

    public static final String SETTLE_DATE = "settle_date";

    public static final String INFO_ORDER = "info_order";

}
