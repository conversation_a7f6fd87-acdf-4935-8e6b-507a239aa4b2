<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OpenChannelInfoMapper">

    <select id="selectOneByChannelCode" resultType="com.zhongyixin.resale.biz.entity.OpenChannelInfo">
        select * from open_channel_info
                 where deleted = 0 and
                       channel_code = #{channelCode} and
                       enabled = #{enabled}
    </select>

    <select id="fuzzyFindByChannelCode" resultType="com.zhongyixin.resale.biz.entity.OpenChannelInfo">
        select * from open_channel_info
        where deleted = 0 and
            channel_code like concat('%',#{channelCode},'%')  and
            enabled = #{enabled}
    </select>

</mapper>
