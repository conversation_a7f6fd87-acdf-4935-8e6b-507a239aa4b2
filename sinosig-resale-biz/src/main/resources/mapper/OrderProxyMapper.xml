<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OrderProxyMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,create_time,update_time,order_no,serial_no,payee_account,payee_name,amount,platform_code,pay_channel,status,deleted,remark,ret_code,trade_date,trade_finish_date,id_card,phone,flag_card ,city_code,brabank_name,brabank_num
    </sql>

    <select id="getOrdersByStatus" resultType="com.zhongyixin.resale.biz.entity.OrderProxy">
        select * from order_proxy where deleted = 0
        <if test="platformCode != null and platformCode != ''">
            and platform_code = #{platformCode}
        </if>
        <if test="payChannel != null and payChannel != ''">
            and pay_channel = #{payChannel}
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="dateTime != null">
            and create_time <![CDATA[ < ]]> #{dateTime}
        </if>
    </select>
    <select id="getOrderBySerialNoAndPlfmCode" resultType="com.zhongyixin.resale.biz.entity.OrderProxy">
        select * from order_proxy where deleted = 0 and platform_code = #{platformCode} and serial_no = #{serialNo}
    </select>
    <select id="getByOrderNo" resultType="com.zhongyixin.resale.biz.entity.OrderProxy">
        select * from order_proxy where deleted = 0 and order_no = #{orderNo}
    </select>
    <select id="batchQueryOrderProxys" resultType="com.zhongyixin.resale.biz.entity.OrderProxy">
        select * from order_proxy where deleted = 0 and platform_code = #{platformCode}
        <if test="serialNos != null and serialNos.size() > 0">
            and serial_no in
            <foreach collection="serialNos" separator="," open="(" close=")" item="serialNo">
                #{serialNo}
            </foreach>
        </if>
    </select>

</mapper>
