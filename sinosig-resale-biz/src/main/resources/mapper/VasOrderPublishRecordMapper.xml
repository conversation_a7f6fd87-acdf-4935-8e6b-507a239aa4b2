<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.VasOrderPublishRecordMapper">



    <select id="getOneByChildOrderNo" resultType="com.zhongyixin.resale.biz.entity.VasOrderPublishRecord">
        select * from vas_order_publish_record where child_order_no = #{childOrderNo} and deleted = 0 order by create_time desc,id desc limit 1
    </select>

</mapper>
