<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.LianLianPayQueryResultRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhongyixin.resale.biz.entity.LianLianPayQueryResultRecord">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="deleted" property="deleted" />
        <result column="update_time" property="updateTime" />
        <result column="ret_code" property="retCode" />
        <result column="ret_msg" property="retMsg" />
        <result column="no_order" property="noOrder" />
        <result column="oid_partner" property="oidPartner" />
        <result column="oid_paybill" property="oidPaybill" />
        <result column="confirm_code" property="confirmCode" />
        <result column="sign_type" property="signType" />
        <result column="sign" property="sign" />
        <result column="dt_order" property="dtOrder" />
        <result column="money_order" property="moneyOrder" />
        <result column="result_pay" property="resultPay" />
        <result column="settle_date" property="settleDate" />
        <result column="memo" property="memo" />
        <result column="type" property="type" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, deleted, update_time, ret_code, ret_msg, no_order, oid_partner, oid_paybill, confirm_code, sign_type, sign, dt_order, money_order, result_pay, settle_date, memo, type
    </sql>

</mapper>
