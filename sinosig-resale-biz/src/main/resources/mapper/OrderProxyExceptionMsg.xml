<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhongyixin.resale.biz.mapper.OrderProxyExceptionMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zhongyixin.resale.biz.entity.OrderProxyExceptionMsg">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="description_msg" property="descriptionMsg" />
        <result column="order_no" property="orderNo" />
        <result column="remark" property="remark" />
    </resultMap>
    <select id="getAll" resultType="com.zhongyixin.resale.biz.entity.OrderProxyExceptionMsg">
        select * from order_proxy_exception_msg where deleted = 0 order by create_time desc
    </select>

</mapper>
